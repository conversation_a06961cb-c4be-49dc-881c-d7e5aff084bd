#!/bin/bash

# HDSC查询系统网络诊断脚本
# 用于检查Docker容器局域网访问问题

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

echo "=== HDSC查询系统网络诊断 ==="
echo ""

# 1. 检查Docker容器状态
log_info "检查Docker容器状态..."
if sudo docker ps | grep -q "hdsc-query-app"; then
    log_success "容器正在运行"
    container_status=$(sudo docker inspect --format='{{.State.Status}}' hdsc-query-app)
    log_info "容器状态: $container_status"
else
    log_error "容器未运行"
    exit 1
fi

# 2. 检查端口映射
log_info "检查端口映射..."
port_mapping=$(sudo docker port hdsc-query-app 5000)
if [ ! -z "$port_mapping" ]; then
    log_success "端口映射: $port_mapping"
else
    log_error "端口映射失败"
    exit 1
fi

# 3. 检查主机IP地址
log_info "检查主机IP地址..."
HOST_IP=$(hostname -I | awk '{print $1}')
log_info "主机局域网IP: $HOST_IP"

# 4. 检查端口监听状态
log_info "检查端口监听状态..."
if netstat -tlnp 2>/dev/null | grep -q ":5000"; then
    log_success "端口5000正在监听"
    netstat -tlnp 2>/dev/null | grep ":5000"
else
    log_error "端口5000未在监听"
fi

# 5. 检查防火墙状态
log_info "检查防火墙状态..."
if sudo ufw status | grep -q "5000/tcp.*ALLOW"; then
    log_success "防火墙已允许5000端口"
else
    log_warn "防火墙未允许5000端口"
    echo "建议执行: sudo ufw allow 5000/tcp"
fi

# 6. 检查容器内部网络
log_info "检查容器内部网络..."
container_ip=$(sudo docker inspect --format='{{range .NetworkSettings.Networks}}{{.IPAddress}}{{end}}' hdsc-query-app)
if [ ! -z "$container_ip" ]; then
    log_info "容器内部IP: $container_ip"
else
    log_warn "无法获取容器IP"
fi

# 7. 测试本机访问
log_info "测试本机访问..."
if curl -s --connect-timeout 5 http://127.0.0.1:5000 > /dev/null 2>&1; then
    log_success "本机访问正常"
    status_code=$(curl -s -o /dev/null -w "%{http_code}" http://127.0.0.1:5000)
    log_info "HTTP状态码: $status_code"
else
    log_error "本机访问失败"
fi

# 8. 测试局域网访问
log_info "测试局域网访问..."
if curl -s --connect-timeout 5 http://$HOST_IP:5000 > /dev/null 2>&1; then
    log_success "局域网访问正常"
    status_code=$(curl -s -o /dev/null -w "%{http_code}" http://$HOST_IP:5000)
    log_info "HTTP状态码: $status_code"
else
    log_error "局域网访问失败"
fi

# 9. 检查网络接口
log_info "检查网络接口..."
default_interface=$(ip route show default | awk '{print $5}' | head -1)
log_info "默认网络接口: $default_interface"

# 10. 显示访问信息
echo ""
echo "=== 访问信息 ==="
echo "本机访问地址: http://127.0.0.1:5000"
echo "本机访问地址: http://localhost:5000"
echo "局域网访问地址: http://$HOST_IP:5000"
echo ""
echo "=== 故障排除建议 ==="
echo "1. 如果本机可访问但局域网不可访问："
echo "   - 检查防火墙: sudo ufw allow 5000/tcp"
echo "   - 检查路由器端口转发设置"
echo ""
echo "2. 如果完全无法访问："
echo "   - 重启容器: sudo docker-compose restart"
echo "   - 检查容器日志: sudo docker-compose logs hdsc-query-app"
echo ""
echo "3. 如果容器未运行："
echo "   - 启动服务: sudo docker-compose up -d"
echo ""

# 11. 生成快速访问命令
echo "=== 快速访问测试命令 ==="
echo "curl http://$HOST_IP:5000"
echo "curl http://127.0.0.1:5000"
