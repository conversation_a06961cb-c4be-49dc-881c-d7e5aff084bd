# HDSC查询系统 Docker部署指南

## 概述

本文档描述了如何在Ubuntu AMD64平台上使用Docker部署HDSC查询系统。

## 系统要求

- Ubuntu 20.04+ (AMD64)
- Docker 20.10+
- Docker Compose 2.0+
- 至少2GB可用内存
- 至少5GB可用磁盘空间

## 快速部署

### 1. 一键部署

```bash
# 给部署脚本执行权限
chmod +x docker-deploy.sh

# 执行部署
sudo ./docker-deploy.sh
```

### 2. 重新构建部署

```bash
# 重新构建镜像并部署
sudo ./docker-deploy.sh --rebuild
```

## 部署文件说明

### 核心文件

- `Dockerfile` - Docker镜像构建文件
- `docker-compose.yml` - Docker Compose配置文件
- `docker-deploy.sh` - 自动化部署脚本
- `check-deployment.sh` - 部署状态检查脚本

### 配置特点

- **基础镜像**: Python 3.11-slim
- **网络端口**: 5000 (映射到主机5000端口)
- **数据持久化**: 日志、缓存、文档目录挂载
- **健康检查**: 30秒间隔的HTTP健康检查
- **国内优化**: 使用阿里云镜像源加速构建

## 目录挂载

| 容器路径 | 主机路径 | 说明 |
|---------|---------|------|
| /app/logs | ./logs | 应用日志目录 |
| /app/cache | ./cache | 缓存数据目录 |
| /app/AHT | ./AHT | AHT文档目录 |
| /app/BHT | ./BHT | BHT文档目录 |
| /app/HZ | ./HZ | HZ文档目录 |
| /app/config.py | ./config.py | 配置文件 |
| /app/gunicorn_config.py | ./gunicorn_config.py | Gunicorn配置 |

## 管理命令

### 基本操作

```bash
# 查看服务状态
sudo docker-compose ps

# 查看实时日志
sudo docker-compose logs -f hdsc-query-app

# 停止服务
sudo docker-compose down

# 重启服务
sudo docker-compose restart

# 进入容器
sudo docker exec -it hdsc-query-app bash
```

### 状态检查

```bash
# 运行健康检查脚本
./check-deployment.sh
```

### 镜像管理

```bash
# 查看镜像
sudo docker images | grep hdsc-query-app

# 删除镜像
sudo docker rmi hdsc-query-app:latest

# 清理未使用的镜像
sudo docker image prune
```

## 访问应用

- **Web界面**: http://localhost:5000
- **健康检查**: http://localhost:5000/health (如果配置了健康检查端点)

## 故障排除

### 常见问题

1. **容器启动失败**
   ```bash
   # 查看详细日志
   sudo docker-compose logs hdsc-query-app
   
   # 检查容器状态
   sudo docker ps -a
   ```

2. **端口冲突**
   ```bash
   # 检查端口占用
   sudo netstat -tlnp | grep 5000
   
   # 修改docker-compose.yml中的端口映射
   ```

3. **权限问题**
   ```bash
   # 确保目录权限正确
   sudo chown -R $USER:$USER logs cache
   chmod 755 logs cache
   ```

4. **内存不足**
   ```bash
   # 检查系统资源
   free -h
   df -h
   
   # 查看容器资源使用
   sudo docker stats hdsc-query-app
   ```

### 日志分析

```bash
# 查看应用启动日志
sudo docker-compose logs hdsc-query-app | grep -i "error\|warning"

# 查看最近的错误
sudo docker-compose logs --tail=50 hdsc-query-app | grep -i error

# 实时监控日志
sudo docker-compose logs -f hdsc-query-app
```

## 性能优化

### 资源限制

在`docker-compose.yml`中添加资源限制：

```yaml
services:
  hdsc-query-app:
    # ... 其他配置
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '0.5'
        reservations:
          memory: 512M
          cpus: '0.25'
```

### 缓存优化

- 日志目录和缓存目录已挂载到主机，重启容器不会丢失数据
- 可以定期清理日志文件以节省空间

## 安全建议

1. **网络安全**
   - 仅在需要时暴露端口到外网
   - 使用防火墙限制访问

2. **数据安全**
   - 定期备份挂载的数据目录
   - 确保敏感配置文件权限正确

3. **更新维护**
   - 定期更新基础镜像
   - 监控安全漏洞

## 备份与恢复

### 备份

```bash
# 备份数据目录
tar -czf hdsc-backup-$(date +%Y%m%d).tar.gz logs cache AHT BHT HZ

# 备份配置文件
cp config.py config.py.backup
cp docker-compose.yml docker-compose.yml.backup
```

### 恢复

```bash
# 停止服务
sudo docker-compose down

# 恢复数据
tar -xzf hdsc-backup-YYYYMMDD.tar.gz

# 重启服务
sudo docker-compose up -d
```

## 联系支持

如果遇到问题，请检查：
1. 运行`./check-deployment.sh`获取详细状态
2. 查看容器日志获取错误信息
3. 确认系统资源充足
4. 验证网络连接正常
