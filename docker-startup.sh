#!/bin/bash

# Docker启动和验证脚本
# 解决snap Docker连接问题

set -e

GREEN='\033[0;32m'
YELLOW='\033[0;33m'
RED='\033[0;31m'
NC='\033[0m'

log_info() { echo -e "${GREEN}[INFO]${NC} $1"; }
log_warn() { echo -e "${YELLOW}[WARN]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }

# 检查Docker是否可用
check_docker() {
    if sudo docker version > /dev/null 2>&1; then
        return 0
    else
        return 1
    fi
}

# 强制重启Docker
force_restart_docker() {
    log_info "强制重启Docker服务..."
    
    # 停止Docker
    sudo snap stop docker 2>/dev/null || true
    sleep 5
    
    # 清理可能的残留进程
    sudo pkill -f dockerd 2>/dev/null || true
    sudo pkill -f docker-containerd 2>/dev/null || true
    sleep 2
    
    # 启动Docker
    sudo snap start docker
    sleep 10
    
    # 验证启动
    local max_attempts=12
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        log_info "尝试连接Docker (第 $attempt/$max_attempts 次)..."
        
        if check_docker; then
            log_success "Docker连接成功！"
            return 0
        fi
        
        sleep 5
        attempt=$((attempt + 1))
    done
    
    log_error "Docker启动失败，已尝试 $max_attempts 次"
    return 1
}

# 主启动流程
main() {
    log_info "开始Docker启动流程..."
    
    # 首先检查当前状态
    if check_docker; then
        log_success "Docker已经在运行"
        sudo docker version
        return 0
    fi
    
    log_warn "Docker未运行，开始启动..."
    
    # 尝试简单启动
    log_info "尝试简单启动..."
    sudo snap start docker 2>/dev/null || true
    sleep 10
    
    if check_docker; then
        log_success "Docker简单启动成功！"
        return 0
    fi
    
    # 尝试重启
    log_info "尝试重启Docker..."
    sudo snap restart docker 2>/dev/null || true
    sleep 15
    
    if check_docker; then
        log_success "Docker重启成功！"
        return 0
    fi
    
    # 强制重启
    log_warn "需要强制重启Docker..."
    if force_restart_docker; then
        log_success "Docker强制重启成功！"
        return 0
    fi
    
    # 最后的诊断
    log_error "Docker启动失败，进行诊断..."
    echo ""
    echo "=== Docker诊断信息 ==="
    echo "Snap服务状态:"
    sudo snap services docker || true
    echo ""
    echo "Docker进程:"
    ps aux | grep docker | grep -v grep || echo "未找到Docker进程"
    echo ""
    echo "Socket文件:"
    ls -la /var/run/docker.sock 2>/dev/null || echo "Socket文件不存在"
    echo ""
    
    return 1
}

# 执行主流程
main

# 如果成功，显示Docker信息
if [ $? -eq 0 ]; then
    echo ""
    log_info "Docker版本信息:"
    sudo docker version
    echo ""
    log_info "Docker已就绪，可以执行其他操作"
fi
