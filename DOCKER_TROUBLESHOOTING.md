# Docker容器管理问题解决指南

## 问题描述

在Ubuntu系统上使用snap安装的Docker时，可能会遇到容器无法正常停止的权限问题：

```
Error response from daemon: cannot stop container: permission denied
```

## 解决方案

### 1. 快速解决方法

使用我们提供的修复工具：

```bash
# 查看问题
./hdsc fix

# 或直接使用修复脚本
./docker-fix.sh --clean-restart
```

### 2. 手动解决步骤

#### 步骤1：强制停止Docker服务
```bash
sudo snap stop docker
```

#### 步骤2：重新启动Docker服务
```bash
sudo snap start docker
```

#### 步骤3：清理并重启容器
```bash
sudo docker-compose down
sudo docker-compose up -d
```

### 3. 完整的管理工具

我们提供了三个管理工具：

#### 3.1 快速命令工具 `./hdsc`
```bash
./hdsc status    # 查看状态
./hdsc start     # 启动服务
./hdsc stop      # 停止服务（自动处理权限问题）
./hdsc restart   # 重启服务
./hdsc logs      # 查看日志
./hdsc shell     # 进入容器
./hdsc check     # 健康检查
./hdsc fix       # 修复问题
```

#### 3.2 问题修复工具 `./docker-fix.sh`
```bash
./docker-fix.sh --force-stop      # 强制停止容器
./docker-fix.sh --restart-docker  # 重启Docker服务
./docker-fix.sh --clean-restart   # 完全清理重启
./docker-fix.sh --check          # 检查状态
```

#### 3.3 完整管理界面 `./docker-manager.sh`
```bash
./docker-manager.sh  # 打开交互式管理界面
```

## 常见问题及解决方法

### 问题1：容器无法停止
**症状**：`docker-compose down` 报权限错误

**解决**：
```bash
./hdsc fix
# 选择选项3：完全清理重启
```

### 问题2：Docker服务异常
**症状**：`Cannot connect to the Docker daemon`

**解决**：
```bash
sudo snap restart docker
# 等待几秒后
./hdsc start
```

### 问题3：端口被占用
**症状**：容器启动失败，端口5000被占用

**解决**：
```bash
# 查找占用端口的进程
sudo netstat -tlnp | grep :5000
# 或
sudo ss -tlnp | grep :5000

# 停止占用进程后重启
./hdsc restart
```

### 问题4：容器健康检查失败
**症状**：容器状态显示unhealthy

**解决**：
```bash
# 查看详细日志
./hdsc logs

# 重启容器
./hdsc restart

# 如果问题持续，完全重置
./docker-fix.sh --clean-restart
```

## 预防措施

### 1. 定期维护
```bash
# 每周运行一次健康检查
./hdsc check

# 定期清理Docker资源
sudo docker system prune -f
```

### 2. 监控资源使用
```bash
# 查看容器资源使用
./hdsc stats

# 查看系统资源
free -h
df -h
```

### 3. 备份重要数据
```bash
# 定期备份
./hdsc backup

# 备份配置文件
cp docker-compose.yml docker-compose.yml.backup
cp config.py config.py.backup
```

## Docker服务管理

### Snap Docker服务命令
```bash
# 启动Docker
sudo snap start docker

# 停止Docker
sudo snap stop docker

# 重启Docker
sudo snap restart docker

# 查看Docker状态
sudo snap services docker
```

### 权限问题处理
如果遇到权限问题，通常是因为：
1. Docker守护进程异常
2. 容器进程卡死
3. 文件系统权限问题

**解决方法**：
1. 重启Docker服务
2. 强制清理容器
3. 检查目录权限

## 应急处理流程

当遇到严重问题时，按以下顺序操作：

1. **保存数据**
   ```bash
   ./hdsc backup
   ```

2. **完全重置**
   ```bash
   ./docker-fix.sh --clean-restart
   ```

3. **验证恢复**
   ```bash
   ./hdsc check
   ```

4. **测试功能**
   ```bash
   curl http://localhost:5000
   ```

## 日志分析

### 查看容器日志
```bash
# 实时日志
./hdsc logs

# 最近的错误
sudo docker-compose logs --tail=50 hdsc-query-app | grep -i error

# 特定时间段的日志
sudo docker-compose logs --since="2024-01-01T00:00:00" hdsc-query-app
```

### 系统日志
```bash
# 查看snap服务日志
sudo journalctl -u snap.docker.dockerd.service -f

# 查看系统资源
dmesg | tail -20
```

## 性能优化建议

1. **定期清理**
   - 清理未使用的镜像和容器
   - 清理日志文件

2. **资源监控**
   - 监控内存使用
   - 监控磁盘空间

3. **配置优化**
   - 调整容器资源限制
   - 优化应用配置

## 联系支持

如果问题仍然存在：

1. 运行完整诊断：`./hdsc check`
2. 收集日志：`./hdsc logs > problem.log`
3. 记录错误信息和操作步骤
4. 提供系统信息：`uname -a`, `docker version`

## 总结

通过使用我们提供的管理工具，您可以：
- 快速诊断和解决Docker问题
- 自动处理权限问题
- 安全地管理容器生命周期
- 监控应用健康状态

记住最重要的命令：
- `./hdsc status` - 快速查看状态
- `./hdsc fix` - 解决问题
- `./hdsc check` - 完整健康检查
