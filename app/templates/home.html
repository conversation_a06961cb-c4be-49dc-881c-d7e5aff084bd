{% extends "base.html" %}

{% block title %}太享查询系统 - 首页{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 引入侧边栏模板 -->
    {% include 'sidebar.html' %}

    <!-- 主内容区 -->
    <div class="main-content">
        <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pb-2 mb-3 border-bottom">
            <h1 class="h2">首页</h1>
            <div class="h6 text-muted">
                <span class="badge bg-primary">{{ version }}</span>
            </div>
        </div>

        <!-- 工具卡片区域 -->
        <div class="row g-4 mb-4">
            <!-- 系统状态卡片 -->
            <div class="col-md-6 col-lg-3">
                <div class="card h-100 border-0 shadow-sm">
                    <div class="card-body">
                        <div class="d-flex align-items-center mb-3">
                            <div class="icon-box bg-primary-light me-3">
                                <i class="bi bi-activity"></i>
                            </div>
                            <h5 class="card-title mb-0">系统状态</h5>
                        </div>
                        <h2 class="mb-2">
                            {% if stats.system_status == 'normal' %}
                                <span class="text-success">
                                    <i class="bi bi-check-circle"></i> 正常
                                </span>
                            {% else %}
                                <span class="text-warning">
                                    <i class="bi bi-exclamation-triangle"></i> 未知
                                </span>
                            {% endif %}
                        </h2>
                        <p class="card-text text-muted">
                            API状态: 
                            {% if stats.api_status == 'online' %}
                                <span class="badge bg-success">在线</span>
                            {% else %}
                                <span class="badge bg-danger">离线</span>
                            {% endif %}
                        </p>
                    </div>
                </div>
            </div>
            
            <!-- 待处理订单卡片 -->
            <div class="col-md-6 col-lg-3">
                <div class="card h-100 border-0 shadow-sm">
                    <div class="card-body">
                        <div class="d-flex align-items-center mb-3">
                            <div class="icon-box bg-info-light me-3">
                                <i class="bi bi-calendar2-check"></i>
                            </div>
                            <h5 class="card-title mb-0">今日待处理</h5>
                        </div>
                        <h2 class="mb-2">{{ stats.today_orders }}</h2>
                        <p class="card-text text-muted">
                            <a href="javascript:void(0);" onclick="AppNavigation.filterByDate('{{ today }}')" class="text-decoration-none">
                                点击查看详情 <i class="bi bi-arrow-right"></i>
                            </a>
                        </p>
                    </div>
                </div>
            </div>
            
            <!-- 逾期订单卡片 -->
            <div class="col-md-6 col-lg-3">
                <div class="card h-100 border-0 shadow-sm">
                    <div class="card-body">
                        <div class="d-flex align-items-center mb-3">
                            <div class="icon-box bg-warning-light me-3">
                                <i class="bi bi-exclamation-circle"></i>
                            </div>
                            <h5 class="card-title mb-0">逾期订单</h5>
                        </div>
                        <h2 class="mb-2">{{ stats.overdue_orders }}</h2>
                        <p class="card-text text-muted">
                            <a href="/overdue" class="text-decoration-none">
                                点击查看详情 <i class="bi bi-arrow-right"></i>
                            </a>
                        </p>
                    </div>
                </div>
            </div>
            
            <!-- 快速搜索卡片 -->
            <div class="col-md-6 col-lg-3">
                <div class="card h-100 border-0 shadow-sm">
                    <div class="card-body">
                        <div class="d-flex align-items-center mb-3">
                            <div class="icon-box bg-success-light me-3">
                                <i class="bi bi-search"></i>
                            </div>
                            <h5 class="card-title mb-0">快速搜索</h5>
                        </div>
                        <form id="quickSearchForm" class="quick-search-form">
                            <div class="input-group mb-2">
                                <input type="text" class="form-control" id="quickSearch" 
                                       placeholder="客户姓名/手机号">
                                <button class="btn btn-primary" type="submit">
                                    <i class="bi bi-search"></i>
                                </button>
                            </div>
                        </form>
                        <p class="card-text text-muted small">
                            输入客户姓名或手机号快速查询
                        </p>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 第二行工具卡片 -->
        <div class="row g-4 mb-4">
            <!-- 常用工具卡片 -->
            <div class="col-md-6">
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-white py-3">
                        <h5 class="card-title mb-0">常用工具</h5>
                    </div>
                    <div class="card-body">
                        <div class="row g-3">
                            <div class="col-6 col-sm-4">
                                <a href="{{ url_for('main.query.filter_data') }}" class="quick-link">
                                    <div class="quick-link-icon bg-primary-light">
                                        <i class="bi bi-calendar-date"></i>
                                    </div>
                                    <span>日期筛选</span>
                                </a>
                            </div>
                            <div class="col-6 col-sm-4">
                                <a href="{{ url_for('main.query.overdue_orders') }}" class="quick-link">
                                    <div class="quick-link-icon bg-warning-light">
                                        <i class="bi bi-exclamation-diamond"></i>
                                    </div>
                                    <span>逾期订单</span>
                                </a>
                            </div>
                            {% if user.has_permission('full') %}
                            <div class="col-6 col-sm-4">
                                <a href="{{ url_for('main.summary_view') }}" class="quick-link">
                                    <div class="quick-link-icon bg-info-light">
                                        <i class="bi bi-pie-chart"></i>
                                    </div>
                                    <span>数据汇总</span>
                                </a>
                            </div>
                            {% endif %}
                            <!-- 可以根据需要添加更多快捷工具 -->
                            <div class="col-6 col-sm-4">
                                <a href="#" class="quick-link" id="calculatorLink">
                                    <div class="quick-link-icon bg-success-light">
                                        <i class="bi bi-calculator"></i>
                                    </div>
                                    <span>计算器</span>
                                </a>
                            </div>
                            <div class="col-6 col-sm-4">
                                <a href="#" class="quick-link" id="calendarLink">
                                    <div class="quick-link-icon bg-secondary-light">
                                        <i class="bi bi-calendar3"></i>
                                    </div>
                                    <span>日历</span>
                                </a>
                            </div>
                            <div class="col-6 col-sm-4">
                                <a href="#" class="quick-link" id="todoListLink">
                                    <div class="quick-link-icon bg-danger-light">
                                        <i class="bi bi-check2-square"></i>
                                    </div>
                                    <span>待办事项</span>
                                </a>
                            </div>
                            <div class="col-6 col-sm-4">
                                <a href="{{ url_for('main.contract_generator') }}" class="quick-link">
                                    <div class="quick-link-icon bg-primary-light">
                                        <i class="bi bi-file-earmark-text"></i>
                                    </div>
                                    <span>合同生成</span>
                                </a>
                            </div>
                            <div class="col-6 col-sm-4">
                                <a href="{{ url_for('main.receipt_generator') }}" class="quick-link">
                                    <div class="quick-link-icon bg-success-light">
                                        <i class="bi bi-receipt"></i>
                                    </div>
                                    <span>回执单生成</span>
                                </a>
                            </div>
                            <div class="col-6 col-sm-4">
                                <a href="#" class="quick-link" id="qrcodeGeneratorLink">
                                    <div class="quick-link-icon bg-purple-light">
                                        <i class="bi bi-qr-code"></i>
                                    </div>
                                    <span>二维码生成</span>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 通知公告卡片 -->
            <div class="col-md-6">
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-white py-3 d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0">通知公告</h5>
                        <span class="badge bg-primary">3 条新消息</span>
                    </div>
                    <div class="card-body p-0">
                        <ul class="list-group list-group-flush notice-list">
                            <li class="list-group-item d-flex align-items-center px-3 py-3">
                                <div class="notice-icon bg-primary-light me-3">
                                    <i class="bi bi-bell"></i>
                                </div>
                                <div class="notice-content">
                                    <h6 class="mb-1">系统更新通知</h6>
                                    <p class="mb-1 text-muted small">系统将于今晚22:00-23:00进行例行维护更新</p>
                                    <span class="text-muted smaller">2023-09-10 15:30</span>
                                </div>
                            </li>
                            <li class="list-group-item d-flex align-items-center px-3 py-3">
                                <div class="notice-icon bg-success-light me-3">
                                    <i class="bi bi-check-circle"></i>
                                </div>
                                <div class="notice-content">
                                    <h6 class="mb-1">新功能上线</h6>
                                    <p class="mb-1 text-muted small">数据汇总功能已上线，欢迎使用体验反馈</p>
                                    <span class="text-muted smaller">2023-09-08 09:15</span>
                                </div>
                            </li>
                            <li class="list-group-item d-flex align-items-center px-3 py-3">
                                <div class="notice-icon bg-warning-light me-3">
                                    <i class="bi bi-exclamation-triangle"></i>
                                </div>
                                <div class="notice-content">
                                    <h6 class="mb-1">操作提醒</h6>
                                    <p class="mb-1 text-muted small">请注意及时处理逾期订单，以免影响业务进度</p>
                                    <span class="text-muted smaller">2023-09-05 16:45</span>
                                </div>
                            </li>
                        </ul>
                    </div>
                    <div class="card-footer bg-white py-2 text-center">
                        <a href="#" class="text-decoration-none">查看全部通知</a>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 小组件区域 -->
        <div class="row g-4">
            <!-- 日历小组件 -->
            <div class="col-md-6">
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-white py-3">
                        <h5 class="card-title mb-0">日程安排</h5>
                    </div>
                    <div class="card-body">
                        <div id="miniCalendar" class="mini-calendar"></div>
                    </div>
                </div>
            </div>
            
            <!-- 待办事项小组件 -->
            <div class="col-md-6">
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-white py-3 d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0">待办事项</h5>
                        <button class="btn btn-sm btn-primary" id="addTodoBtn">
                            <i class="bi bi-plus"></i> 添加
                        </button>
                    </div>
                    <div class="card-body p-0">
                        <ul class="list-group list-group-flush todo-list" id="todoList">
                            <li class="list-group-item d-flex align-items-center px-3 py-3">
                                <div class="form-check me-3">
                                    <input class="form-check-input" type="checkbox" value="" id="todo1">
                                </div>
                                <div class="todo-content">
                                    <h6 class="mb-1">跟进张三客户的订单变更</h6>
                                    <span class="text-muted smaller">截止日期: 今天</span>
                                </div>
                                <div class="ms-auto">
                                    <span class="badge bg-danger">紧急</span>
                                </div>
                            </li>
                            <li class="list-group-item d-flex align-items-center px-3 py-3">
                                <div class="form-check me-3">
                                    <input class="form-check-input" type="checkbox" value="" id="todo2">
                                </div>
                                <div class="todo-content">
                                    <h6 class="mb-1">处理待审核订单</h6>
                                    <span class="text-muted smaller">截止日期: 明天</span>
                                </div>
                                <div class="ms-auto">
                                    <span class="badge bg-warning">中等</span>
                                </div>
                            </li>
                            <li class="list-group-item d-flex align-items-center px-3 py-3">
                                <div class="form-check me-3">
                                    <input class="form-check-input" type="checkbox" value="" id="todo3">
                                </div>
                                <div class="todo-content">
                                    <h6 class="mb-1">整理本周工作总结</h6>
                                    <span class="text-muted smaller">截止日期: 周五</span>
                                </div>
                                <div class="ms-auto">
                                    <span class="badge bg-info">一般</span>
                                </div>
                            </li>
                        </ul>
                    </div>
                    <div class="card-footer bg-white py-2 text-center">
                        <a href="#" class="text-decoration-none" id="viewAllTodos">查看全部</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block styles %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/pages/home.css') }}">
{% endblock %}

{% block scripts %}
<!-- 性能优化模块 -->
<script src="{{ url_for('static', filename='js/utils/performance-optimizer.js') }}" defer></script>

<!-- 组件模块 -->
<script src="{{ url_for('static', filename='js/components/calculator.js') }}" defer></script>
<script src="{{ url_for('static', filename='js/components/todo.js') }}" defer></script>
<script src="{{ url_for('static', filename='js/components/qrcode.js') }}" defer></script>

<!-- 主页面控制器 -->
<script src="{{ url_for('static', filename='js/pages/home.js') }}" defer></script>

<!-- 计算器模态框 -->
<div class="modal fade" id="calculatorModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-sm">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">计算器</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="calculator">
                    <div class="form-group mb-3">
                        <input type="text" class="form-control text-end" id="calcDisplay" readonly>
                    </div>
                    <div class="calc-buttons">
                        <div class="row g-1 mb-1">
                            <div class="col-3"><button class="btn btn-light w-100">C</button></div>
                            <div class="col-3"><button class="btn btn-light w-100">±</button></div>
                            <div class="col-3"><button class="btn btn-light w-100">%</button></div>
                            <div class="col-3"><button class="btn btn-warning w-100">÷</button></div>
                        </div>
                        <div class="row g-1 mb-1">
                            <div class="col-3"><button class="btn btn-light w-100">7</button></div>
                            <div class="col-3"><button class="btn btn-light w-100">8</button></div>
                            <div class="col-3"><button class="btn btn-light w-100">9</button></div>
                            <div class="col-3"><button class="btn btn-warning w-100">×</button></div>
                        </div>
                        <div class="row g-1 mb-1">
                            <div class="col-3"><button class="btn btn-light w-100">4</button></div>
                            <div class="col-3"><button class="btn btn-light w-100">5</button></div>
                            <div class="col-3"><button class="btn btn-light w-100">6</button></div>
                            <div class="col-3"><button class="btn btn-warning w-100">-</button></div>
                        </div>
                        <div class="row g-1 mb-1">
                            <div class="col-3"><button class="btn btn-light w-100">1</button></div>
                            <div class="col-3"><button class="btn btn-light w-100">2</button></div>
                            <div class="col-3"><button class="btn btn-light w-100">3</button></div>
                            <div class="col-3"><button class="btn btn-warning w-100">+</button></div>
                        </div>
                        <div class="row g-1">
                            <div class="col-6"><button class="btn btn-light w-100">0</button></div>
                            <div class="col-3"><button class="btn btn-light w-100">.</button></div>
                            <div class="col-3"><button class="btn btn-warning w-100">=</button></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 添加待办事项模态框 -->
<div class="modal fade" id="todoModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">添加待办事项</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="todoForm">
                    <div class="mb-3">
                        <label for="todoTitle" class="form-label">标题</label>
                        <input type="text" class="form-control" id="todoTitle" required>
                    </div>
                    <div class="mb-3">
                        <label for="todoDueDate" class="form-label">截止日期</label>
                        <input type="date" class="form-control" id="todoDueDate">
                    </div>
                    <div class="mb-3">
                        <label for="todoPriority" class="form-label">优先级</label>
                        <select class="form-select" id="todoPriority">
                            <option value="low">低</option>
                            <option value="medium">中</option>
                            <option value="high">高</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="todoDesc" class="form-label">备注</label>
                        <textarea class="form-control" id="todoDesc" rows="3"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="saveTodoBtn">保存</button>
            </div>
        </div>
    </div>
</div>

<!-- 二维码生成器模态框 -->
<div class="modal fade" id="qrcodeModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="bi bi-qr-code me-2"></i>二维码生成器
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="qrcodeForm">
                    <div class="mb-3">
                        <label for="qrcodeContent" class="form-label">内容</label>
                        <textarea class="form-control" id="qrcodeContent" rows="4" 
                                placeholder="请输入要生成二维码的内容，如网址、文本、联系方式等..."></textarea>
                        <div class="form-text">支持网址、文本、联系方式等各种内容</div>
                    </div>
                    <div class="mb-3">
                        <label for="qrcodeSize" class="form-label">尺寸</label>
                        <select class="form-select" id="qrcodeSize">
                            <option value="128">128x128</option>
                            <option value="256" selected>256x256</option>
                            <option value="512">512x512</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <button type="button" class="btn btn-primary" id="generateQrcodeBtn">
                            <i class="bi bi-gear-fill me-1"></i>生成二维码
                        </button>
                    </div>
                    <div id="qrcodeResult" class="text-center" style="display: none;">
                        <div class="mb-3">
                            <img id="qrcodeImage" src="" alt="生成的二维码" class="border rounded">
                        </div>
                        <div class="d-flex gap-2 justify-content-center">
                            <button type="button" class="btn btn-success btn-sm" id="downloadQrcodeBtn">
                                <i class="bi bi-download me-1"></i>下载PNG
                            </button>
                            <button type="button" class="btn btn-info btn-sm" id="copyQrcodeBtn">
                                <i class="bi bi-clipboard me-1"></i>复制到剪贴板
                            </button>
                        </div>
                    </div>
                    <div id="qrcodeError" class="alert alert-danger" style="display: none;"></div>
                    <div id="qrcodeLoading" class="text-center" style="display: none;">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">生成中...</span>
                        </div>
                        <div class="mt-2">生成中，请稍候...</div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>
{% endblock %} 