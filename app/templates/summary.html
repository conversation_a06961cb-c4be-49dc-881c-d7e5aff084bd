{% extends "base.html" %}

{% block title %}太享查询_{{ version }} - 数据汇总{% endblock %}

{% block styles %}
<!-- 引入汇总页面核心样式 -->
<link rel="stylesheet" href="{{ url_for('static', filename='css/pages/summary-page.css') }}">
<!-- 引入增强样式 -->
<link rel="stylesheet" href="{{ url_for('static', filename='css/summary-enhanced.css') }}">
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <!-- 引入侧边栏模板 -->
        {% include 'sidebar.html' %}

        <!-- 主内容区 -->
        <div class="col-md-9 col-lg-10 main-content">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pb-2 mb-3 border-bottom">
                <h1 class="h2">数据汇总分析</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <div class="btn-group mr-2">
                        <button type="button" class="btn btn-sm btn-outline-secondary" id="exportChartsBtn">
                            <i class="bi bi-download"></i> 导出图表
                        </button>
                    </div>
                </div>
            </div>

            <!-- 日期范围选择表单 -->
            <div class="card mb-4">
                <div class="card-body">
                    <form method="GET" action="{{ url_for('main.summary_view') }}" class="row g-3" data-show-global-loading="true">
                        <div class="col-md-4">
                            <label for="start_date" class="form-label">开始日期</label>
                            <input type="date" class="form-control" id="start_date" name="start_date" value="{{ start_date or today }}">
                        </div>
                        <div class="col-md-4">
                            <label for="end_date" class="form-label">结束日期</label>
                            <input type="date" class="form-control" id="end_date" name="end_date" value="{{ end_date or today }}">
                        </div>
                        <div class="col-md-4 d-flex align-items-end">
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-search"></i> 查询
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- 图表区域 -->
            <div class="row">
                <!-- 订单汇总图表 -->
                <div class="col-md-6">
                    <div class="card mb-4">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">订单月度汇总</h5>
                            <div class="chart-controls">
                                <select class="form-select form-select-sm chart-type-selector" data-chart="orderChart">
                                    <option value="bar">柱状图</option>
                                    <option value="line">折线图</option>
                                    <option value="pie">饼图</option>
                                </select>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="chart-container">
                                <canvas id="orderChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 逾期汇总图表 -->
                <div class="col-md-6">
                    <div class="card mb-4">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">逾期月度汇总</h5>
                            <div class="chart-controls">
                                <select class="form-select form-select-sm chart-type-selector" data-chart="overdueChart">
                                    <option value="bar">柱状图</option>
                                    <option value="line">折线图</option>
                                </select>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="chart-container">
                                <canvas id="overdueChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 数据表格区域 -->
            <div class="row">
                <!-- 订单汇总表格 -->
                <div class="col-md-6">
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="mb-0">订单数据明细</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-striped table-hover data-table" style="width:100%">
                                    <thead>
                                        <tr>
                                            <th class="dtr-control"></th> <!-- 用于响应式详情控制 -->
                                            <th>月份</th>
                                            <th>电商台数/订单数量</th>
                                            <th>租赁台数/订单数量</th>
                                            <th>总台数/订单数量</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% if order_summary and order_summary|length > 0 %}
                                            {% for item in order_summary %}
                                            <tr>
                                                <td class="dtr-control"></td>
                                                <td>{{ item.月份|default('未知') }}</td>
                                                <td>{{ item.电商台数|default(item.电商订单数量|default(0)) }}</td>
                                                <td>{{ item.租赁台数|default(item.租赁订单数量|default(0)) }}</td>
                                                <td>{{ (item.电商台数|default(item.电商订单数量|default(0))|int) + (item.租赁台数|default(item.租赁订单数量|default(0))|int) }}</td>
                                            </tr>
                                            {% endfor %}
                                        {% else %}
                                            <tr>
                                                <td colspan="5" class="text-center auto-dismiss-alert">暂无数据</td>
                                            </tr>
                                        {% endif %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 逾期汇总表格 -->
                <div class="col-md-6">
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="mb-0">逾期数据明细</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-striped table-hover data-table" style="width:100%">
                                    <thead>
                                        <tr>
                                            <th class="dtr-control"></th> <!-- 用于响应式详情控制 -->
                                            <th>月份</th>
                                            <th>电商逾期订单数</th>
                                            <th>租赁逾期订单数</th>
                                            <th>总逾期订单数</th>
                                            <th>逾期金额</th>
                                            <th>逾期率</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% if overdue_summary and overdue_summary|length > 0 %}
                                            {% for item in overdue_summary %}
                                            <tr>
                                                <td class="dtr-control"></td>
                                                <td>{{ item.月份|default('未知') }}</td>
                                                <td>{{ item.电商逾期订单数量|default(0) }}</td>
                                                <td>{{ item.租赁逾期订单数量|default(0) }}</td>
                                                <td>{{ (item.电商逾期订单数量|default(0)|int) + (item.租赁逾期订单数量|default(0)|int) }}</td>
                                                <td>{{ item.逾期金额|default(0) }}</td>
                                                <td>{{ item.逾期率|default(0) }}%</td>
                                            </tr>
                                            {% endfor %}
                                        {% else %}
                                            <tr>
                                                <td colspan="7" class="text-center auto-dismiss-alert">暂无数据</td>
                                            </tr>
                                        {% endif %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 原始汇总数据表格 -->
            {% if summary_data_results and summary_data_results|length > 0 %}
            <div class="row">
                <div class="col-12">
                    <div class="card mb-4">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">详细汇总数据</h5>
                            <div class="btn-toolbar">
                                <div class="dropdown me-2">
                                    <button class="btn btn-sm btn-outline-primary dropdown-toggle" type="button" id="dataViewSelector" data-bs-toggle="dropdown">
                                        <i class="bi bi-grid"></i> 视图
                                    </button>
                                    <ul class="dropdown-menu view-selector">
                                        <li><a class="dropdown-item active" href="#" data-view="tabs">店铺选项卡</a></li>
                                        <li><a class="dropdown-item" href="#" data-view="table">完整表格</a></li>
                                        <li><a class="dropdown-item" href="#" data-view="cards">指标卡片</a></li>
                                    </ul>
                                </div>
                                <button class="btn btn-sm btn-outline-secondary" id="exportSummaryData">
                                    <i class="bi bi-download"></i> 导出
                                </button>
                            </div>
                        </div>
                        <div class="card-body">
                            <!-- 视图内容将通过JavaScript动态加载 -->
                            <div id="summaryDataContainer"></div>
                            
                            <!-- 用于存储汇总数据的隐藏元素 -->
                            <script id="summaryData" type="application/json">
                                {
                                    "headers": {{ summary_data_headers|tojson }},
                                    "summary": {{ summary_data_results|tojson }},
                                    "timing_stats": {{ timing_stats|tojson }}
                                }
                            </script>
                        </div>
                    </div>
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- 全局加载指示器 -->
<div class="loading-overlay" id="loadingOverlay" style="display: none;">
    <div class="spinner-border text-primary" role="status">
        <span class="visually-hidden">加载中...</span>
    </div>
</div>

{% endblock %}

{% block scripts %}
<!-- 初始化下拉菜单功能 -->
<script>
// 确保在页面完全加载后初始化下拉菜单
window.addEventListener('load', function() {
    console.log('页面完全加载，初始化下拉菜单');
    
    try {
        // 手动为指定的下拉菜单添加点击事件
        var dataViewSelector = document.getElementById('dataViewSelector');
        if (dataViewSelector) {
            dataViewSelector.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                
                var dropdown = document.querySelector('.dropdown-menu.view-selector');
                if (dropdown) {
                    dropdown.classList.toggle('show');
                    dropdown.style.display = dropdown.classList.contains('show') ? 'block' : 'none';
                    dropdown.style.position = 'absolute';
                    dropdown.style.transform = 'translate3d(0px, 38px, 0px)';
                    dropdown.style.top = '0px';
                    dropdown.style.left = '0px';
                    dropdown.style.willChange = 'transform';
                }
            });
            
            // 点击其他区域时关闭下拉菜单
            document.addEventListener('click', function(e) {
                if (!e.target.closest('.dropdown')) {
                    var dropdown = document.querySelector('.dropdown-menu.view-selector');
                    if (dropdown && dropdown.classList.contains('show')) {
                        dropdown.classList.remove('show');
                        dropdown.style.display = 'none';
                    }
                }
            });
            
            // 处理菜单项点击
            var menuItems = document.querySelectorAll('.dropdown-menu.view-selector .dropdown-item');
            menuItems.forEach(function(item) {
                item.addEventListener('click', function(e) {
                    e.preventDefault();
                    
                    // 移除所有active类
                    menuItems.forEach(function(mi) {
                        mi.classList.remove('active');
                    });
                    
                    // 添加active类到当前项
                    this.classList.add('active');
                    
                    // 调用视图切换函数
                    if (typeof switchDataView === 'function') {
                        switchDataView(this.getAttribute('data-view'));
                    }
                    
                    // 关闭下拉菜单
                    var dropdown = document.querySelector('.dropdown-menu.view-selector');
                    if (dropdown) {
                        dropdown.classList.remove('show');
                        dropdown.style.display = 'none';
                    }
                });
            });
        }
    } catch(e) {
        console.error('手动初始化下拉菜单失败:', e);
    }
    
    // 添加CSS样式确保下拉菜单正常显示
    var style = document.createElement('style');
    style.textContent = `
        .dropdown-menu.show {
            display: block !important;
            position: absolute !important;
            z-index: 1000 !important;
        }
    `;
    document.head.appendChild(style);
});
</script>

<!-- 引入增强型数据表格JavaScript -->
<!-- data-table-enhanced.js已在base.html中引用，无需重复引用 -->
<script src="{{ url_for('static', filename='js/summary-enhanced.js') }}"></script>

<!-- 引入主JavaScript文件 -->
<script src="{{ url_for('static', filename='js/main-app.js') }}"></script>
<script>
// 图表数据和实例初始化
var orderChartData = {};
var overdueChartData = {};
var orderChart = null;
var overdueChart = null;

// 从服务器获取的数据
{% if order_chart_data %}
try {
    orderChartData = JSON.parse('{{ order_chart_data|tojson }}');
} catch(e) {
    console.error('解析订单图表数据失败:', e);
}
{% endif %}

{% if overdue_chart_data %}
try {
    overdueChartData = JSON.parse('{{ overdue_chart_data|tojson }}');
} catch(e) {
    console.error('解析逾期图表数据失败:', e);
}
{% endif %}

// 初始化图表和表格
document.addEventListener('DOMContentLoaded', function() {
    console.log('数据汇总页面初始化开始');
    
    // 隐藏加载状态指示器
    hideLoading();
    
    // 重要：首先初始化表格，使用短延迟确保DOM完全加载
    setTimeout(function() {
        initSummaryTables();
    }, 200);
    
    // 然后初始化图表
    setTimeout(function() {
        initCharts();
    }, 300);
    
    // 图表类型选择器
    setupChartTypeSelectors();
    
    // 检测API状态
    checkApiStatus();
    
    // 监听窗口大小变化事件
    window.addEventListener('resize', handleResize);
    
    // 最后优化移动设备显示
    setTimeout(function() {
        optimizeForMobile();
        console.log('数据汇总页面移动端优化完成');
    }, 500);
});

// 初始化所有图表
function initCharts() {
    console.log('开始初始化图表');
    
    // 订单汇总图表
    var orderCtx = document.getElementById('orderChart');
    if (orderCtx && orderChartData && orderChartData.labels) {
        try {
            var ctx = orderCtx.getContext('2d');
            orderChart = new Chart(ctx, {
                type: 'bar',
                data: orderChartData,
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        title: {
                            display: true,
                            text: '月度订单数量趋势'
                        },
                        legend: {
                            position: 'top'
                        }
                    }
                }
            });
            console.log('订单图表初始化成功');
        } catch(err) {
            console.error('订单图表初始化失败:', err);
        }
    } else {
        console.log('订单图表数据不可用或图表元素未找到');
    }
    
    // 逾期汇总图表
    var overdueCtx = document.getElementById('overdueChart');
    if (overdueCtx && overdueChartData && overdueChartData.labels) {
        try {
            var ctx = overdueCtx.getContext('2d');
            overdueChart = new Chart(ctx, {
                type: 'bar',
                data: overdueChartData,
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        title: {
                            display: true,
                            text: '月度逾期趋势'
                        },
                        legend: {
                            position: 'top'
                        }
                    }
                }
            });
            console.log('逾期图表初始化成功');
        } catch(err) {
            console.error('逾期图表初始化失败:', err);
        }
    } else {
        console.log('逾期图表数据不可用或图表元素未找到');
    }
}

// 初始化汇总页面所有表格
function initSummaryTables() {
    console.log('开始初始化汇总页面表格');
    
    const tables = document.querySelectorAll('.data-table');
    console.log(`找到${tables.length}个表格需要初始化`);
    
    tables.forEach((table, index) => {
        console.log(`初始化表格 #${index+1}`);
        
        // 确保表格未被初始化或销毁现有实例
        if ($.fn.DataTable.isDataTable(table)) {
            $(table).DataTable().destroy();
            console.log(`表格 #${index+1} 已存在，已销毁`);
        }
        
        try {
            // 使用main.js中的标准方法
            const dataTable = initDataTable(table);
            if (dataTable) {
                console.log(`表格 #${index+1} 初始化成功`);
            } else {
                console.error(`表格 #${index+1} 初始化失败`);
            }
        } catch(err) {
            console.error(`表格 #${index+1} 初始化失败:`, err);
        }
    });
    
    // 应用增强功能
    try {
        enhanceDataTables();
        console.log('表格增强功能应用成功');
    } catch(e) {
        console.error('表格增强功能应用失败:', e);
    }
}

// 设置图表类型选择器
function setupChartTypeSelectors() {
    var typeSelectors = document.querySelectorAll('.chart-type-selector');
    typeSelectors.forEach(function(selector) {
        selector.addEventListener('change', function() {
            var chartId = this.getAttribute('data-chart');
            var chartType = this.value;
            
            if (chartId === 'orderChart' && orderChart) {
                orderChart.config.type = chartType;
                orderChart.update();
            } else if (chartId === 'overdueChart' && overdueChart) {
                overdueChart.config.type = chartType;
                overdueChart.update();
            }
        });
    });
}

// 窗口大小变化处理
function handleResize() {
    clearTimeout(window.resizeTimer);
    window.resizeTimer = setTimeout(function() {
        try {
            // 重新计算表格布局
            const tables = document.querySelectorAll('.data-table');
            tables.forEach(table => {
                if ($.fn.DataTable.isDataTable(table)) {
                    $(table).DataTable().columns.adjust().responsive.recalc();
                }
            });
            
            // 重新优化移动端显示
            optimizeForMobile();
            
            console.log('窗口大小变化处理完成');
        } catch(e) {
            console.error('窗口大小变化处理失败:', e);
        }
    }, 250);
}

// 导出图表功能
function exportCharts() {
    try {
        // 创建一个临时画布
        var tempCanvas = document.createElement('canvas');
        tempCanvas.width = 1200;
        tempCanvas.height = 800;
        
        // 将订单图表合并到临时画布
        if (orderChart) {
            var orderCanvas = document.getElementById('orderChart');
            var ctx = tempCanvas.getContext('2d');
            ctx.drawImage(orderCanvas, 0, 0, tempCanvas.width, tempCanvas.height / 2);
        }
        
        // 将逾期图表合并到临时画布
        if (overdueChart) {
            var overdueCanvas = document.getElementById('overdueChart');
            var ctx = tempCanvas.getContext('2d');
            ctx.drawImage(overdueCanvas, 0, tempCanvas.height / 2, tempCanvas.width, tempCanvas.height / 2);
        }
        
        // 导出为图片
        var link = document.createElement('a');
        link.download = '数据汇总图表_' + new Date().toISOString().slice(0, 10) + '.png';
        link.href = tempCanvas.toDataURL('image/png');
        link.click();
    } catch (err) {
        console.error('导出图表失败:', err);
        alert('导出图表失败，请重试');
    }
}

// 客户搜索函数
function searchCustomer() {
    var customerName = document.getElementById('customerName').value.trim();
    if (!customerName) {
        alert('请输入客户姓名');
        return;
    }
    
    window.location.href = "/?customerName=" + encodeURIComponent(customerName) + "&tab=customer";
}

// 检测API状态
function checkApiStatus() {
    var apiStatusElement = document.getElementById('apiStatus');
    if (!apiStatusElement) return;
    
    // 设置超时时间（5秒）
    const timeout = 5000;
    
    // 创建AbortController用于超时控制
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), timeout);
    
    fetch("{{ url_for('api.ping') }}", {
        signal: controller.signal
    })
    .then(function(response) {
        clearTimeout(timeoutId);
        if (!response.ok) {
            throw new Error('API响应状态: ' + response.status);
        }
        return response.json();
    })
    .then(function(data) {
        if (data.status === 'ok') {
            apiStatusElement.innerHTML = '<span class="badge bg-success">正常</span>';
        } else {
            apiStatusElement.innerHTML = '<span class="badge bg-warning">异常</span>';
        }
    })
    .catch(function(error) {
        clearTimeout(timeoutId);
        console.error('API状态检查失败:', error);
        if (error.name === 'AbortError') {
            apiStatusElement.innerHTML = '<span class="badge bg-danger">超时</span>';
        } else {
            apiStatusElement.innerHTML = '<span class="badge bg-danger">连接失败</span>';
        }
        
        // 5秒后重试
        setTimeout(checkApiStatus, 5000);
    });
}
</script>
{% endblock %}