<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}太享查询{% endblock %}</title>
    <!-- jQuery -->
    <script src="{{ url_for('static', filename='vendor/jquery/jquery.min.js') }}"></script>
    <!-- 引入导出管理器 -->
    <script src="{{ url_for('static', filename='js/export-manager.js') }}"></script>
    <!-- Bootstrap JS Bundle with <PERSON>per -->
    <script src="{{ url_for('static', filename='vendor/bootstrap/js/bootstrap.bundle.min.js') }}"></script>
    <!-- Bootstrap CSS -->
    <link href="{{ url_for('static', filename='vendor/bootstrap/css/bootstrap.min.css') }}" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="{{ url_for('static', filename='vendor/bootstrap-icons/font/bootstrap-icons.min.css') }}" rel="stylesheet">
    <!-- DataTables CSS -->
    <link href="{{ url_for('static', filename='vendor/datatables/css/dataTables.bootstrap5.min.css') }}" rel="stylesheet">
    <!-- DataTables Responsive CSS -->
    <link href="{{ url_for('static', filename='vendor/datatables/css/responsive.bootstrap5.min.css') }}" rel="stylesheet">
    <!-- Design Tokens System - 设计令牌系统(最高优先级) -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/design-tokens.css') }}">
    <!-- CSS Variables System - 兼容性支持 -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/variables.css') }}">
    <!-- Component System - BEM架构组件 -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/components/button.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/components/data-table.css') }}">
    <!-- Base Layout CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/base-layout.css') }}">
    <!-- Legacy CSS - 逐步移除 -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/styles.css') }}">
    <!-- 逾期订单表格修复CSS - 待重构 -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/overdue-table-fix.css') }}">
    <!-- 统一表格样式 - 确保所有表格样式一致 -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/unified-table-styles.css') }}">
    {% block styles %}{% endblock %}
</head>
<body>
    {% block body %}
    <!-- 闪现消息 -->
    <div class="flash-container">
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                <div class="alert alert-{{ category }} alert-dismissible fade show auto-dismiss-alert" role="alert">
                    {{ message }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
                {% endfor %}
            {% endif %}
        {% endwith %}
    </div>
    
    <!-- 全局样式 -->
    <style>
        /* 自定义样式 */
        body {
            font-family: "Microsoft YaHei", sans-serif;
            background-color: #f8f9fa;
            overflow-x: hidden;
        }
        
        /* 侧边栏样式 */
        .sidebar {
            position: fixed;
            top: 0;
            bottom: 0;
            left: 0;
            z-index: 100;
            padding: 0;
            box-shadow: 0 2px 5px 0 rgba(0,0,0,.05);
            background-color: #fff;
            transition: all 0.3s;
            width: 250px;
            display: flex;
            flex-direction: column;
        }
        
        .sidebar-header {
            padding: 15px;
            text-align: center;
            flex-shrink: 0;
        }
        
        .sidebar-header .logo {
            max-width: 80px;
            margin-bottom: 10px;
        }
        
        .sidebar-header h4 {
            margin-bottom: 5px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .sidebar .nav {
            flex-grow: 1;
            overflow-y: auto;
        }
        
        .sidebar-footer {
            padding: 15px;
            text-align: center;
            border-top: 1px solid #eee;
            flex-shrink: 0;
        }
        
        #collapseToggle {
            width: 100%;
            margin-top: 10px;
        }
        
        .nav-item {
            margin: 2px 0;
        }
        
        .nav-link {
            color: #495057;
            border-radius: 0;
            padding: 0.75rem 1rem;
            transition: all 0.2s;
        }
        
        .nav-link:hover {
            background-color: #f8f9fa;
            color: #007bff;
        }
        
        .nav-link.active {
            background-color: #e9ecef;
            color: #007bff;
            font-weight: 500;
        }
        
        .sidebar-collapsed {
            margin-left: -250px;
        }
        
        /* 侧边栏折叠样式 */
        .sidebar-collapsed + .main-content {
            margin-left: 0;
            width: 100%;
        }
        
        /* 折叠后的还原按钮 */
        .sidebar-expand-btn {
            position: fixed;
            bottom: 20px;
            left: 20px;
            z-index: 101;
            display: none;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background-color: #fff;
            color: #495057;
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
            text-align: center;
            line-height: 40px;
            cursor: pointer;
        }
        
        .sidebar-toggle {
            position: fixed;
            top: 10px;
            left: 10px;
            z-index: 101;
            display: none;
            cursor: pointer;
            background-color: rgba(255, 255, 255, 0.9);
            border-radius: 50%;
            width: 40px;
            height: 40px;
            text-align: center;
            line-height: 40px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
        }
        
        .main-content {
            transition: all 0.3s;
            margin-left: 250px;
            width: calc(100% - 250px);
        }
        
        .main-content-expanded {
            margin-left: 0;
            width: 100%;
        }
        
        /* 折叠时的侧边栏样式 */
        @media (min-width: 769px) {
            .sidebar-collapsed {
                width: 60px;
            }
            
            .sidebar-collapsed .sidebar-header h4,
            .sidebar-collapsed .sidebar-header .user-info,
            .sidebar-collapsed .sidebar-header hr,
            .sidebar-collapsed .nav-text,
            .sidebar-collapsed .sidebar-footer {
                display: none;
            }
            
            .sidebar-collapsed .nav-link {
                text-align: center;
                padding: 0.75rem 0;
            }
            
            .sidebar-collapsed .nav-link .bi {
                margin-right: 0;
                font-size: 1.3rem;
            }
            
            .sidebar-collapsed + .main-content {
                margin-left: 60px;
                width: calc(100% - 60px);
            }
            
            .sidebar-expand-btn {
                display: none;
            }
            
            .sidebar-collapsed ~ .sidebar-expand-btn {
                display: block;
            }
        }
        
        /* 手机设备适配 */
        @media (max-width: 768px) {
            .sidebar {
                margin-left: -250px;
            }
            
            .sidebar-toggle {
                display: block;
            }
            
            .main-content {
                margin-left: 0;
                width: 100%;
            }
            
            .sidebar-active {
                margin-left: 0;
                box-shadow: 2px 0 5px rgba(0,0,0,0.2);
            }
            
            .overlay {
                display: none;
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background-color: rgba(0, 0, 0, 0.5);
                z-index: 99;
            }
            
            .overlay-active {
                display: block;
            }
            
            /* 移动端隐藏展开按钮 */
            .sidebar-expand-btn {
                display: none !important;
            }
            
            /* 移动端导航按钮样式 */
            .sidebar-toggle {
                background-color: white;
                border: 1px solid rgba(0,0,0,0.1);
                box-shadow: 0 2px 4px rgba(0,0,0,0.1);
                font-size: 1.2rem;
            }
            
            /* 移动端侧边栏按钮样式优化 */
            .sidebar .nav-link {
                width: 100%;
                text-align: left;
                padding: 0.75rem 1rem;
                margin: 0;
                border-radius: 0;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
            }
            
            .sidebar .nav-link .bi {
                width: 24px;
                text-align: center;
                margin-right: 10px;
            }
            
            .sidebar .nav-item {
                width: 100%;
                margin: 0;
            }
            
            .sidebar .btn-link {
                width: 100%;
                text-align: left;
                padding: 0.75rem 1rem;
                margin: 0;
                border-radius: 0;
            }
            
            /* 移动端表格优化 */
            .table-responsive {
                max-width: 100%;
                overflow-x: auto;
            }
            
            /* 移动端页面标题优化 */
            h1.h2 {
                font-size: 1.5rem;
            }
        }
        
        /* 加载指示器 */
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            z-index: 9999;
            display: none; /* 默认隐藏 */
        }
        
        .loading-spinner {
            border: 5px solid #f3f3f3;
            border-top: 5px solid #3498db;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            animation: spin 2s linear infinite;
            margin-bottom: 10px;
        }
        
        .loading-text {
            color: white;
            font-size: 16px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        /* 数据单元格折叠样式 */
        .cell-content {
            position: relative;
            max-width: 150px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            cursor: pointer;
            display: block;
            transition: all 0.3s ease;
            color: #333;
        }

        .cell-content:after {
            content: '';
            position: absolute;
            right: 0;
            top: 0;
            height: 100%;
            width: 30px;
            background: linear-gradient(to right, rgba(255,255,255,0), rgba(255,255,255,1));
            pointer-events: none;
        }

        .cell-content.expanded {
            white-space: normal;
            word-break: break-word;
            max-width: none;
            overflow: visible;
        }

        .cell-content.expanded:after {
            display: none;
        }

        /* 鼠标悬停状态 */
        .cell-content:hover {
            color: #007bff;
        }

        /* 为展开内容添加视觉提示 */
        .cell-content:before {
            content: '';
            display: inline-block;
            width: 0;
            height: 0;
            margin-right: 4px;
            transition: transform 0.3s ease;
        }

        .cell-content.expanded:before {
            transform: rotate(90deg);
        }

        /* 移动端样式优化 */
        @media (max-width: 768px) {
            .cell-content {
                max-width: 120px;
            }
            
            table.dataTable.dtr-inline.collapsed > tbody > tr > td.dtr-control:before,
            table.dataTable.dtr-inline.collapsed > tbody > tr > th.dtr-control:before {
                top: 50%;
                transform: translateY(-50%);
                background-color: #007bff;
                box-shadow: 0 0 0.2rem rgba(0, 123, 255, 0.5);
            }
            
            .dtr-data {
                word-break: break-word;
                max-width: calc(100vw - 100px);
            }
        }
        
        /* 自动消失提示 */
        .auto-dismiss-alert {
            animation: fadeOut 0.5s ease 3s forwards;
            position: relative;
        }
        
        @keyframes fadeOut {
            0% { opacity: 1; }
            100% { opacity: 0; height: 0; padding-top: 0; padding-bottom: 0; margin: 0; }
        }
        
        /* DataTables响应式详情样式 */
        .dtr-details {
            width: 100%;
        }
        
        .dtr-details tr {
            border-bottom: 1px solid #f0f0f0;
        }
        
        .dtr-details td {
            padding: 8px;
        }
        
        .dtr-title {
            font-weight: bold;
            color: #555;
            min-width: 120px;
        }
        
        .dtr-data {
            word-break: break-word;
        }
        
        .nav-link .bi {
            margin-right: 10px;
            font-size: 1.1rem;
            width: 24px;
            text-align: center;
        }
        
        .sidebar-collapsed .sidebar-header h4,
        .sidebar-collapsed .sidebar-header .user-info,
        .sidebar-collapsed .sidebar-header hr,
        .sidebar-collapsed .nav-text,
        .sidebar-collapsed .sidebar-footer {
            display: none;
        }
        
        /* 侧边栏折叠时隐藏搜索表单和API状态 */
        .sidebar-collapsed .sidebar-search,
        .sidebar-collapsed .api-status-container {
            display: none;
        }
        
        .sidebar-collapsed .nav-link {
            text-align: center;
            padding: 0.75rem 0;
        }
        
        .sidebar-search {
            padding: 0 15px;
            margin-bottom: 0;
        }
        
        .sidebar-search form {
            margin-bottom: 10px;
        }
        
        .sidebar-search .form-control-sm {
            font-size: 0.875rem;
            border-radius: 4px;
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
        }
        
        .sidebar-search .form-control-sm:focus {
            background-color: #fff;
            box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
            border-color: #80bdff;
        }
        
        .sidebar-search .btn-sm {
            padding: 0.25rem 0.5rem;
            font-size: 0.875rem;
            border-radius: 4px;
            transition: all 0.2s;
        }
        
        .sidebar-search .btn-sm:hover {
            background-color: #007bff;
            color: #fff;
            border-color: #007bff;
        }
        
        .sidebar-search label {
            font-size: 0.875rem;
            font-weight: 500;
            color: #495057;
            margin-bottom: 0.25rem;
        }
        
        .api-status-container {
            padding: 0 15px 10px;
            margin-top: 10px;
            font-size: 0.875rem;
        }
        
        /* 移动端DataTables控制列样式 */
        .dtr-control {
            position: relative;
            cursor: pointer;
            width: 35px !important;
            min-width: 35px !important;
            display: table-cell !important;
        }
        
        .dtr-control:before {
            content: '+';
            display: inline-block;
            color: #31b131;
            font-weight: bold;
            font-size: 1.2em;
        }
        
        tr.dt-hasChild td.dtr-control:before {
            content: '-';
            color: #d33333;
        }
        
        table.dataTable > thead > tr > th.dtr-control,
        table.dataTable > tbody > tr > td.dtr-control {
            display: table-cell !important;
            max-width: 35px;
            min-width: 35px;
            width: 35px !important;
            padding-right: 5px;
            padding-left: 5px;
        }
        
        /* 日期选择器样式 - 确保所有页面都显示日历图标 */
        input[type="date"] {
            position: relative;
        }
        
        /* 自定义日期选择器图标 */
        input[type="date"]::-webkit-calendar-picker-indicator {
            position: absolute;
            right: 8px;
            top: 50%;
            transform: translateY(-50%);
            opacity: 1;
            cursor: pointer;
        }
        
        /* 日期选择器位置调整 */
        input[type="date"]::-webkit-datetime-edit {
            padding-right: 28px; /* 为图标留出空间 */
        }
        
        /* 调整弹出日历的位置 */
        ::-webkit-calendar-picker-indicator {
            position: relative; 
        }
        
        /* 兼容性处理 */
        input[type="date"]::-webkit-inner-spin-button {
            display: none;
        }
        
        input[type="date"]::-webkit-clear-button {
            display: none;
        }
    </style>
    
    <!-- 添加账单状态和数据显示的自定义样式 -->
    <style>
        /* 账单状态颜色 */
        tr.status-账单日 {
            background-color: #FFF4B084 !important;
        }
        
        tr.status-逾期未还 {
            background-color: #FFFFC7CE !important;
        }
        
        tr.status-逾期还款 {
            background-color: #FFFFEB9C !important;
        }
        
        tr.status-提前还款 {
            background-color: #FFD9E1F2 !important;
        }
        
        tr.status-按时还款 {
            background-color: #FFC6EFCE !important;
        }
        
        /* 金额显示美化 */
        td.money-positive {
            color: #28a745;
            font-weight: 500;
        }
        
        td.money-zero {
            color: #6c757d;
        }
        
        /* 设备型号显示 */
        td.device-model {
            font-family: monospace;
            background-color: #f8f9fa;
            padding: 0.2rem 0.5rem;
            border-radius: 3px;
        }
        
        /* 嵌套数据结构的展示样式 */
        .nested-data-info {
            display: flex;
            flex-wrap: wrap;
            gap: 5px;
        }
        
        .nested-data-item {
            padding: 2px 5px;
            background-color: #f8f9fa;
            border-radius: 3px;
            font-size: 0.9em;
        }
        
        .nested-data-label {
            font-weight: 500;
            color: #495057;
        }
        
        .nested-data-value {
            color: #212529;
        }
        
        /* 备注内容样式 */
        .remarks-content {
            max-width: 250px;
            white-space: normal;
            overflow: hidden;
            text-overflow: ellipsis;
            position: relative;
            padding: 5px;
            border-radius: 4px;
            background-color: #f8f9fa;
            font-size: 0.85em;
            line-height: 1.4;
            max-height: 60px;
            cursor: pointer;
            transition: max-height 0.3s, max-width 0.3s;
        }
        
        .remarks-content:hover {
            max-height: 300px;
            max-width: 350px;
            overflow: auto;
            z-index: 100;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
    </style>
    
    <!-- 侧边栏切换遮罩 -->
    <div class="overlay" id="sidebarOverlay"></div>
    
    <!-- 侧边栏切换按钮（移动端） -->
    <div class="sidebar-toggle d-md-none" id="sidebarToggle">
        <i class="bi bi-list"></i>
    </div>
    
    <!-- 加载指示器 -->
    <div class="loading-overlay" id="loadingOverlay">
        <div class="loading-spinner"></div>
        <div class="loading-text">正在加载数据，请稍候...</div>
    </div>
    
    <!-- 主内容 -->
    <main>
        {% block content %}{% endblock %}
    </main>
    {% endblock %}
    
    <!-- jQuery -->
    <script src="{{ url_for('static', filename='vendor/jquery/jquery.min.js') }}"></script>
    <!-- Bootstrap Bundle with Popper -->
    <script src="{{ url_for('static', filename='vendor/bootstrap/js/bootstrap.bundle.min.js') }}"></script>
    <!-- DataTables JS -->
    <script src="{{ url_for('static', filename='vendor/datatables/js/jquery.dataTables.min.js') }}"></script>
    <script src="{{ url_for('static', filename='vendor/datatables/js/dataTables.bootstrap5.min.js') }}"></script>
    <!-- DataTables Responsive JS -->
    <script src="{{ url_for('static', filename='vendor/datatables/js/dataTables.responsive.min.js') }}"></script>
    <script src="{{ url_for('static', filename='vendor/datatables/js/responsive.bootstrap5.min.js') }}"></script>
    <!-- Chart.js -->
    <script src="{{ url_for('static', filename='vendor/chart.js/chart.min.js') }}"></script>
    <!-- Core JavaScript Modules - 核心模块(必须按顺序加载) -->
    <script src="{{ url_for('static', filename='js/event-manager.js') }}"></script>
    <script src="{{ url_for('static', filename='js/dom-utils.js') }}"></script>
    <script src="{{ url_for('static', filename='js/core/api-cache-manager.js') }}"></script>
    <script src="{{ url_for('static', filename='js/core/navigation.js') }}"></script>
    <!-- 页面自动刷新修复器 - 解决30秒后自动刷新的问题 -->
    <script src="{{ url_for('static', filename='js/page-refresh-fix.js') }}"></script>

    <!-- 加载指示器控制函数 -->
    <script>
        function showLoading() {
            document.getElementById('loadingOverlay').style.display = 'flex';
            
            // 添加自动超时，防止加载指示器无限显示
            window.loadingTimeout = setTimeout(function() {
                hideLoading();
            }, 15000); // 15秒后自动关闭
        }
        
        function hideLoading() {
            document.getElementById('loadingOverlay').style.display = 'none';
            
            // 清除超时计时器
            if (window.loadingTimeout) {
                clearTimeout(window.loadingTimeout);
            }
        }

        // 页面加载完成后，确保加载指示器已隐藏
        document.addEventListener('DOMContentLoaded', function() {
            hideLoading();
            
            // 监听beforeunload事件，确保不会留下加载指示器
            window.addEventListener('beforeunload', function() {
                hideLoading();
            });
        });

        // 侧边栏功能由SidebarManagerV2统一管理，移除旧的jQuery代码避免冲突
        $(document).ready(function() {
            // 侧边栏相关功能已迁移到SidebarManagerV2模块
            
            // 配置DataTables全局默认值
            $.extend($.fn.dataTable.defaults, {
                // 设置中文语言 - 移除CDN依赖，使用本地配置
                language: {
                    search: "搜索筛选:",
                    searchPlaceholder: "输入关键词进行筛选",
                    lengthMenu: "显示 _MENU_ 条记录",
                    zeroRecords: "无匹配数据",
                    info: "显示第 _START_ 至 _END_ 条记录，共 _TOTAL_ 条",
                    infoEmpty: "显示第 0 至 0 条记录，共 0 条",
                    infoFiltered: "(从 _MAX_ 条记录中筛选)",
                    emptyTable: "表中数据为空",
                    loadingRecords: "载入中...",
                    processing: "处理中...",
                    paginate: {
                        first: "首页",
                        previous: "上页",
                        next: "下页",
                        last: "末页"
                    },
                    aria: {
                        sortAscending: ": 以升序排列此列",
                        sortDescending: ": 以降序排列此列"
                    }
                },
                // 启用响应式特性
                responsive: {
                    details: {
                        type: 'column',
                        target: 'tr td:first-child'
                    }
                },
                // 启用分页
                paging: true,
                // 每页记录数选项
                lengthMenu: [[10, 25, 50, 100, -1], [10, 25, 50, 100, "全部"]],
                // 默认排序 - 修改为订单编号降序排列（假设订单编号在第2列，因为第1列是详情控制列）
                order: [[1, 'desc']],
                // 自动宽度
                autoWidth: false,
                // 为响应式控制列添加样式定义
                columnDefs: [{
                    className: 'dtr-control',
                    orderable: false,
                    targets: 0
                }]
            });

            // 表格单元格内容折叠/展开功能
            $(document).on('click', '.cell-content', function() {
                $(this).toggleClass('expanded');
            });
            
            // 表单提交时显示加载指示器
            $('form').on('submit', function() {
                showLoading();
                
                // 记录这是从侧边栏导航的，以便后续处理
                sessionStorage.setItem('navigatedFromSidebar', 'true');
                
                // 记录导航目标，特别关注逾期订单查询
                const linkText = $(this).text().trim();
                if (linkText.includes('逾期订单查询')) {
                    sessionStorage.setItem('navigatingToOverdue', 'true');
                }
            });
            
            // 绑定AJAX请求事件
            $(document).ajaxStart(function() {
                showLoading();
            }).ajaxStop(function() {
                hideLoading();
            });
        });
    </script>
    
    <!-- 移动端输入优化脚本 -->
    <script>
        // 移动端虚拟键盘弹出修复
        document.addEventListener('DOMContentLoaded', function() {
            // 针对iOS设备的特殊修复
            if (/iPhone|iPad|iPod/i.test(navigator.userAgent)) {
                // 获取所有表单输入元素
                const formInputs = document.querySelectorAll('input[type="text"], input[type="search"], input[type="date"]');
                
                formInputs.forEach(input => {
                    // 防止自动滚动和重新定位
                    input.addEventListener('focus', function(e) {
                        // 阻止默认的滚动行为
                        e.preventDefault();
                        
                        // 记录当前滚动位置
                        const scrollPos = window.scrollY;
                        
                        // 延迟执行，确保阻止iOS的自动滚动
                        setTimeout(function() {
                            // 恢复原始滚动位置
                            window.scrollTo(0, scrollPos);
                            
                            // 针对客户搜索框的特殊处理
                            if (input.id === 'customerName') {
                                // 确保搜索表单保持在原位置
                                const form = document.getElementById('customerSearchForm');
                                if (form) {
                                    form.style.position = 'relative';
                                    form.style.transform = 'none';
                                    form.style.webkitTransform = 'none';
                                }
                            }
                        }, 100);
                    });
                    
                    // 防止iOS自动缩放
                    input.style.fontSize = '16px';
                });
                
                // 聚焦时禁用页面缩放
                const meta = document.querySelector('meta[name="viewport"]');
                const originalContent = meta ? meta.getAttribute('content') : '';
                
                // 添加事件监听器来处理所有输入元素
                document.addEventListener('focusin', function(e) {
                    if (e.target.tagName === 'INPUT' || e.target.tagName === 'TEXTAREA') {
                        // 禁用用户缩放
                        if (meta) {
                            meta.setAttribute('content', 'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no');
                        }
                    }
                });
                
                // 失去焦点时恢复原始设置
                document.addEventListener('focusout', function(e) {
                    if (e.target.tagName === 'INPUT' || e.target.tagName === 'TEXTAREA') {
                        // 恢复原始设置
                        if (meta && originalContent) {
                            meta.setAttribute('content', originalContent);
                        }
                    }
                });
            }
            
            // 特别处理客户搜索表单
            const customerSearchForm = document.getElementById('customerSearchForm');
            if (customerSearchForm) {
                const customerNameInput = document.getElementById('customerName');
                
                if (customerNameInput) {
                    // 阻止虚拟键盘导致的重新定位
                    customerNameInput.addEventListener('focus', function(e) {
                        // 防止页面重排
                        document.body.style.height = document.body.offsetHeight + 'px';
                        document.body.style.overflow = 'hidden';
                        
                        // 强制保持客户搜索表单位置
                        customerSearchForm.style.position = 'relative';
                        customerSearchForm.style.transform = 'none';
                        customerSearchForm.style.top = 'auto';
                        customerSearchForm.style.left = 'auto';
                        
                        // 针对Android设备
                        if (/Android/i.test(navigator.userAgent)) {
                            // 记录原始滚动位置
                            const originalScrollPos = window.scrollY;
                            
                            // 延迟执行，确保阻止Android的自动滚动
                            setTimeout(function() {
                                window.scrollTo(0, originalScrollPos);
                            }, 50);
                        }
                    });
                    
                    // 输入字段失去焦点时恢复正常布局
                    customerNameInput.addEventListener('blur', function() {
                        document.body.style.height = '';
                        document.body.style.overflow = '';
                    });
                }
            }
        });
    </script>
    
    <!-- 备注展开提示脚本 -->
    <script>
        // 当表格初始化完成后添加提示
        $(document).on('init.dt', function(e, settings) {
            // 延迟一下确保DOM已完全渲染
            setTimeout(function() {
                // 查找所有带有expandable-content类的元素
                const expandableElements = document.querySelectorAll('.expandable-content');
                
                // 如果页面上有这些元素，显示一次性提示
                if (expandableElements.length > 0 && !sessionStorage.getItem('remarkTipShown')) {
                    // 创建提示元素
                    const tipElement = document.createElement('div');
                    tipElement.className = 'alert alert-info alert-dismissible fade show auto-dismiss-alert';
                    tipElement.style.position = 'fixed';
                    tipElement.style.bottom = '20px';
                    tipElement.style.right = '20px';
                    tipElement.style.zIndex = '9999';
                    tipElement.style.maxWidth = '300px';
                    tipElement.style.boxShadow = '0 2px 10px rgba(0,0,0,0.2)';
                    
                    tipElement.innerHTML = `
                        <strong>提示：</strong> 点击备注文本可展开查看完整内容
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    `;
                    
                    // 添加到文档中
                    document.body.appendChild(tipElement);
                    
                    // 设置会话存储，避免重复显示
                    sessionStorage.setItem('remarkTipShown', 'true');
                    
                    // 5秒后自动关闭
                    setTimeout(function() {
                        $(tipElement).alert('close');
                    }, 5000);
                }
            }, 500);
        });
    </script>
    
    <!-- 通用侧边栏修复器 - 优先加载 -->
    <script src="{{ url_for('static', filename='js/sidebar-universal-fix.js') }}"></script>
    
    <!-- Enhanced DataTable JS -->
    <script src="{{ url_for('static', filename='js/data-table-enhanced.js') }}"></script>
    
    <!-- API状态修复器 -->
    <script src="{{ url_for('static', filename='js/api-status-fix.js') }}"></script>
    
    <!-- 模块化JavaScript - 按依赖顺序加载 -->
    <!-- 1. 核心基础模块 -->
    <script src="{{ url_for('static', filename='js/core/app-core.js') }}"></script>
    
    <!-- 2. API数据管理模块 -->
    <script src="{{ url_for('static', filename='js/modules/api-data-manager.js') }}"></script>
    
    <!-- 3. 导航管理模块 -->
    <script src="{{ url_for('static', filename='js/modules/navigation-manager.js') }}"></script>
    
    <!-- 4. 表格管理模块 -->
    <script src="{{ url_for('static', filename='js/modules/table-manager.js') }}"></script>
    
    <!-- 4.5. 统一表格管理器 -->
    <script src="{{ url_for('static', filename='js/modules/unified-table-manager.js') }}"></script>
    
    <!-- 5. 数据加载模块 -->
    <script src="{{ url_for('static', filename='js/modules/data-loader.js') }}"></script>

    <!-- 6. 数据显示模块 -->
    <script src="{{ url_for('static', filename='js/modules/data-display.js') }}"></script>

    <!-- 7. 导出处理模块 -->
    <script src="{{ url_for('static', filename='js/modules/export-handler.js') }}"></script>
    <!-- 8. 侧边栏管理模块 V2 -->
    <script src="{{ url_for('static', filename='js/modules/sidebar-manager-v2.js') }}"></script>
    
    <!-- 9. 主应用初始化模块 -->
    <script src="{{ url_for('static', filename='js/main-app.js') }}"></script>
    
    <!-- 开发环境测试脚本 (生产环境请移除) -->
    <!-- <script src="{{ url_for('static', filename='js/test-modules.js') }}"></script> -->

    <!-- 客户搜索功能测试工具 (开发环境) -->
    {% if config.get('DEBUG', False) %}
    <script src="{{ url_for('static', filename='js/customer-search-test.js') }}"></script>
    {% endif %}
    
    <!-- 简化的兼容性脚本 -->
    <script>
        // 向后兼容的jQuery ready处理
        $(document).ready(function() {
            console.log('jQuery ready - 模块化JavaScript已接管大部分功能');
            
            // 只保留必要的兼容性代码
            // 其他功能已由 TaixiangApp.App 模块处理
        });
    </script>
    
    {% block scripts %}{% endblock %}
</body>
</html>