/**
 * 图表管理模块
 * 负责处理订单汇总图表和逾期汇总图表的初始化、类型切换和导出功能
 */

class ChartManager {
    constructor() {
        this.orderChart = null;
        this.overdueChart = null;
        this.orderChartData = {};
        this.overdueChartData = {};
        
        // 绑定方法上下文
        this.exportCharts = this.exportCharts.bind(this);
        this.handleChartTypeChange = this.handleChartTypeChange.bind(this);
    }

    /**
     * 初始化图表管理器
     * @param {Object} orderChartData - 订单图表数据
     * @param {Object} overdueChartData - 逾期图表数据
     */
    initialize(orderChartData = {}, overdueChartData = {}) {
        this.orderChartData = orderChartData;
        this.overdueChartData = overdueChartData;
        
        console.log('ChartManager: 开始初始化图表');
        
        // 延迟初始化图表以确保DOM完全加载
        setTimeout(() => {
            this.initializeCharts();
            this.setupChartTypeSelectors();
        }, 300);
    }

    /**
     * 初始化所有图表
     */
    initializeCharts() {
        console.log('ChartManager: 开始初始化图表实例');
        
        this.initializeOrderChart();
        this.initializeOverdueChart();
    }

    /**
     * 初始化订单汇总图表
     */
    initializeOrderChart() {
        const orderCtx = document.getElementById('orderChart');
        if (!orderCtx) {
            console.log('ChartManager: 订单图表容器未找到');
            return;
        }

        if (!this.orderChartData || !this.orderChartData.labels) {
            console.log('ChartManager: 订单图表数据不可用');
            return;
        }

        try {
            const ctx = orderCtx.getContext('2d');
            this.orderChart = new Chart(ctx, {
                type: 'bar',
                data: this.orderChartData,
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        title: {
                            display: true,
                            text: '月度订单数量趋势',
                            font: {
                                size: 16,
                                weight: 'bold'
                            }
                        },
                        legend: {
                            position: 'top',
                            labels: {
                                boxWidth: 12,
                                font: {
                                    size: 12
                                }
                            }
                        },
                        tooltip: {
                            backgroundColor: 'rgba(0,0,0,0.8)',
                            titleColor: '#fff',
                            bodyColor: '#fff',
                            borderColor: '#007bff',
                            borderWidth: 1
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                font: {
                                    size: 11
                                }
                            }
                        },
                        x: {
                            ticks: {
                                font: {
                                    size: 11
                                }
                            }
                        }
                    }
                }
            });
            console.log('ChartManager: 订单图表初始化成功');
        } catch (err) {
            console.error('ChartManager: 订单图表初始化失败:', err);
        }
    }

    /**
     * 初始化逾期汇总图表
     */
    initializeOverdueChart() {
        const overdueCtx = document.getElementById('overdueChart');
        if (!overdueCtx) {
            console.log('ChartManager: 逾期图表容器未找到');
            return;
        }

        if (!this.overdueChartData || !this.overdueChartData.labels) {
            console.log('ChartManager: 逾期图表数据不可用');
            return;
        }

        try {
            const ctx = overdueCtx.getContext('2d');
            this.overdueChart = new Chart(ctx, {
                type: 'bar',
                data: this.overdueChartData,
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        title: {
                            display: true,
                            text: '月度逾期趋势',
                            font: {
                                size: 16,
                                weight: 'bold'
                            }
                        },
                        legend: {
                            position: 'top',
                            labels: {
                                boxWidth: 12,
                                font: {
                                    size: 12
                                }
                            }
                        },
                        tooltip: {
                            backgroundColor: 'rgba(220,53,69,0.8)',
                            titleColor: '#fff',
                            bodyColor: '#fff',
                            borderColor: '#dc3545',
                            borderWidth: 1
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                font: {
                                    size: 11
                                }
                            }
                        },
                        x: {
                            ticks: {
                                font: {
                                    size: 11
                                }
                            }
                        }
                    }
                }
            });
            console.log('ChartManager: 逾期图表初始化成功');
        } catch (err) {
            console.error('ChartManager: 逾期图表初始化失败:', err);
        }
    }

    /**
     * 设置图表类型选择器
     */
    setupChartTypeSelectors() {
        const typeSelectors = document.querySelectorAll('.chart-type-selector');
        typeSelectors.forEach(selector => {
            selector.addEventListener('change', this.handleChartTypeChange);
        });
    }

    /**
     * 处理图表类型变化
     * @param {Event} event - 选择器变化事件
     */
    handleChartTypeChange(event) {
        const chartId = event.target.getAttribute('data-chart');
        const chartType = event.target.value;
        
        try {
            if (chartId === 'orderChart' && this.orderChart) {
                this.orderChart.config.type = chartType;
                this.orderChart.update('active');
                console.log(`ChartManager: 订单图表类型已切换为 ${chartType}`);
            } else if (chartId === 'overdueChart' && this.overdueChart) {
                this.overdueChart.config.type = chartType;
                this.overdueChart.update('active');
                console.log(`ChartManager: 逾期图表类型已切换为 ${chartType}`);
            }
        } catch (err) {
            console.error('ChartManager: 图表类型切换失败:', err);
        }
    }

    /**
     * 导出图表功能
     * 将两个图表合并到一个画布并导出为PNG图片
     */
    exportCharts() {
        try {
            // 创建一个临时画布
            const tempCanvas = document.createElement('canvas');
            tempCanvas.width = 1200;
            tempCanvas.height = 800;
            const ctx = tempCanvas.getContext('2d');
            
            // 设置背景色
            ctx.fillStyle = '#ffffff';
            ctx.fillRect(0, 0, tempCanvas.width, tempCanvas.height);
            
            let hasContent = false;
            
            // 将订单图表合并到临时画布
            if (this.orderChart) {
                const orderCanvas = document.getElementById('orderChart');
                if (orderCanvas) {
                    ctx.drawImage(orderCanvas, 0, 0, tempCanvas.width, tempCanvas.height / 2);
                    hasContent = true;
                }
            }
            
            // 将逾期图表合并到临时画布
            if (this.overdueChart) {
                const overdueCanvas = document.getElementById('overdueChart');
                if (overdueCanvas) {
                    ctx.drawImage(overdueCanvas, 0, tempCanvas.height / 2, tempCanvas.width, tempCanvas.height / 2);
                    hasContent = true;
                }
            }
            
            if (!hasContent) {
                throw new Error('没有可导出的图表内容');
            }
            
            // 添加标题和时间戳
            ctx.fillStyle = '#333333';
            ctx.font = 'bold 20px Arial, sans-serif';
            ctx.textAlign = 'center';
            ctx.fillText('数据汇总图表', tempCanvas.width / 2, 30);
            
            ctx.font = '14px Arial, sans-serif';
            ctx.fillText(`导出时间: ${new Date().toLocaleString()}`, tempCanvas.width / 2, tempCanvas.height - 20);
            
            // 导出为图片
            const link = document.createElement('a');
            link.download = `数据汇总图表_${new Date().toISOString().slice(0, 10)}.png`;
            link.href = tempCanvas.toDataURL('image/png');
            
            // 触发下载
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            
            console.log('ChartManager: 图表导出成功');
        } catch (err) {
            console.error('ChartManager: 导出图表失败:', err);
            this.showError('导出图表失败，请重试');
        }
    }

    /**
     * 处理窗口大小变化
     */
    handleResize() {
        // 防抖处理
        clearTimeout(this.resizeTimer);
        this.resizeTimer = setTimeout(() => {
            try {
                if (this.orderChart) {
                    this.orderChart.resize();
                }
                if (this.overdueChart) {
                    this.overdueChart.resize();
                }
                console.log('ChartManager: 图表大小已调整');
            } catch (err) {
                console.error('ChartManager: 调整图表大小失败:', err);
            }
        }, 250);
    }

    /**
     * 显示错误消息
     * @param {string} message - 错误消息
     */
    showError(message) {
        // 创建简单的错误提示
        const alert = document.createElement('div');
        alert.className = 'alert alert-danger alert-dismissible fade show position-fixed';
        alert.style.cssText = 'top: 20px; right: 20px; z-index: 9999; max-width: 400px;';
        alert.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        document.body.appendChild(alert);
        
        // 3秒后自动移除
        setTimeout(() => {
            if (alert.parentNode) {
                alert.parentNode.removeChild(alert);
            }
        }, 3000);
    }

    /**
     * 销毁图表实例
     */
    destroy() {
        try {
            if (this.orderChart) {
                this.orderChart.destroy();
                this.orderChart = null;
            }
            if (this.overdueChart) {
                this.overdueChart.destroy();
                this.overdueChart = null;
            }
            console.log('ChartManager: 图表实例已销毁');
        } catch (err) {
            console.error('ChartManager: 销毁图表实例失败:', err);
        }
    }
}

// 导出模块
window.ChartManager = ChartManager;