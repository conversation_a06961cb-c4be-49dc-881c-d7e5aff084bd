/**
 * 模块化重构测试脚本
 * 用于验证所有模块是否正确加载和初始化
 */

(function() {
    'use strict';
    
    console.log('开始模块化重构测试...');
    
    // 测试结果收集
    const testResults = {
        passed: 0,
        failed: 0,
        errors: []
    };
    
    // 测试函数
    function test(testName, testFn) {
        try {
            if (testFn()) {
                console.log(`✅ ${testName} - PASSED`);
                testResults.passed++;
                return true;
            } else {
                console.error(`❌ ${testName} - FAILED`);
                testResults.failed++;
                testResults.errors.push(testName);
                return false;
            }
        } catch (error) {
            console.error(`💥 ${testName} - ERROR:`, error);
            testResults.failed++;
            testResults.errors.push(`${testName}: ${error.message}`);
            return false;
        }
    }
    
    // 等待DOM加载完成后执行测试
    document.addEventListener('DOMContentLoaded', function() {
        setTimeout(runTests, 1000); // 等待1秒确保所有模块初始化完成
    });
    
    function runTests() {
        console.log('\n=== 模块加载测试 ===');
        
        // 1. 测试核心模块
        test('TaixiangApp 命名空间存在', () => {
            return typeof window.TaixiangApp === 'object';
        });
        
        test('TaixiangApp.Utils 模块存在', () => {
            return typeof TaixiangApp.Utils === 'object';
        });
        
        test('TaixiangApp.State 模块存在', () => {
            return typeof TaixiangApp.State === 'object';
        });
        
        // 2. 测试API数据管理模块
        test('TaixiangApp.ApiDataManager 模块存在', () => {
            return typeof TaixiangApp.ApiDataManager === 'object';
        });
        
        test('ApiDataManager.storeData 函数存在', () => {
            return typeof TaixiangApp.ApiDataManager.storeData === 'function';
        });
        
        test('ApiDataManager.fetchApi 函数存在', () => {
            return typeof TaixiangApp.ApiDataManager.fetchApi === 'function';
        });
        
        // 3. 测试导航管理模块
        test('TaixiangApp.Navigation 模块存在', () => {
            return typeof TaixiangApp.Navigation === 'object';
        });
        
        test('Navigation.searchCustomer 函数存在', () => {
            return typeof TaixiangApp.Navigation.searchCustomer === 'function';
        });
        
        test('Navigation.filterByDate 函数存在', () => {
            return typeof TaixiangApp.Navigation.filterByDate === 'function';
        });
        
        // 4. 测试表格管理模块
        test('TaixiangApp.TableManager 模块存在', () => {
            return typeof TaixiangApp.TableManager === 'object';
        });
        
        test('TableManager.enhanceDataTables 函数存在', () => {
            return typeof TaixiangApp.TableManager.enhanceDataTables === 'function';
        });
        
        test('TableManager.initDataTable 函数存在', () => {
            return typeof TaixiangApp.TableManager.initDataTable === 'function';
        });
        
        // 5. 测试侧边栏管理模块
        test('TaixiangApp.SidebarManager 类存在', () => {
            return typeof TaixiangApp.SidebarManager === 'function';
        });
        
        test('TaixiangApp.SidebarFunctionManager 模块存在', () => {
            return typeof TaixiangApp.SidebarFunctionManager === 'object';
        });
        
        // 6. 测试主应用模块
        test('TaixiangApp.App 模块存在', () => {
            return typeof TaixiangApp.App === 'object';
        });
        
        test('App.init 函数存在', () => {
            return typeof TaixiangApp.App.init === 'function';
        });
        
        test('App 已初始化', () => {
            return TaixiangApp.App.initialized === true;
        });
        
        console.log('\n=== 向后兼容性测试 ===');
        
        // 7. 测试向后兼容性
        test('全局 showLoading 函数存在', () => {
            return typeof window.showLoading === 'function';
        });
        
        test('全局 hideLoading 函数存在', () => {
            return typeof window.hideLoading === 'function';
        });
        
        test('全局 AppNavigation 对象存在', () => {
            return typeof window.AppNavigation === 'object';
        });
        
        test('全局 enhanceDataTables 函数存在', () => {
            return typeof window.enhanceDataTables === 'function';
        });
        
        test('全局 ApiDataManager 对象存在', () => {
            return typeof window.ApiDataManager === 'object';
        });
        
        test('全局 viewCustomerSummary 函数存在', () => {
            return typeof window.viewCustomerSummary === 'function';
        });
        
        console.log('\n=== 功能测试 ===');
        
        // 8. 测试核心功能
        test('加载指示器元素存在', () => {
            return document.getElementById('loadingOverlay') !== null;
        });
        
        test('侧边栏元素存在', () => {
            return document.querySelector('.sidebar') !== null;
        });
        
        test('客户搜索表单存在', () => {
            return document.getElementById('customerSearchForm') !== null;
        });
        
        test('日期筛选表单存在', () => {
            return document.getElementById('dateFilterForm') !== null;
        });
        
        // 9. 测试API缓存功能
        test('API缓存功能正常', () => {
            // 测试缓存存储和获取
            const testData = { test: 'data' };
            TaixiangApp.ApiDataManager.storeData('test_endpoint', { param: 'value' }, testData);
            const retrieved = TaixiangApp.ApiDataManager.getData('test_endpoint', { param: 'value' });
            return JSON.stringify(retrieved) === JSON.stringify(testData);
        });
        
        // 10. 测试状态管理
        test('全局状态管理正常', () => {
            return typeof TaixiangApp.State.overdueData === 'object' &&
                   typeof TaixiangApp.State.resetOverdueState === 'function';
        });
        
        console.log('\n=== 测试结果汇总 ===');
        console.log(`✅ 通过: ${testResults.passed}`);
        console.log(`❌ 失败: ${testResults.failed}`);
        
        if (testResults.failed > 0) {
            console.log('失败的测试:');
            testResults.errors.forEach(error => {
                console.log(`  - ${error}`);
            });
            
            console.log('\n🚨 模块化重构存在问题，请检查模块加载顺序和依赖关系');
        } else {
            console.log('\n🎉 所有测试通过！模块化重构成功！');
        }
        
        // 在页面上显示测试结果
        showTestResults();
    }
    
    function showTestResults() {
        // 创建测试结果显示元素
        const resultDiv = document.createElement('div');
        resultDiv.id = 'module-test-results';
        resultDiv.style.cssText = `
            position: fixed;
            top: 10px;
            right: 10px;
            background: ${testResults.failed > 0 ? '#fff5f5' : '#f0fff4'};
            border: 2px solid ${testResults.failed > 0 ? '#fed7d7' : '#9ae6b4'};
            border-radius: 8px;
            padding: 16px;
            max-width: 300px;
            z-index: 10000;
            font-family: monospace;
            font-size: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        `;
        
        const status = testResults.failed > 0 ? 
            `❌ 模块测试失败 (${testResults.failed} 项)` : 
            `✅ 模块测试通过 (${testResults.passed} 项)`;
        
        resultDiv.innerHTML = `
            <div style="font-weight: bold; margin-bottom: 8px;">${status}</div>
            <div>通过: ${testResults.passed}</div>
            <div>失败: ${testResults.failed}</div>
            <button onclick="this.parentElement.remove()" style="margin-top: 8px; padding: 4px 8px; font-size: 10px;">关闭</button>
        `;
        
        document.body.appendChild(resultDiv);
        
        // 10秒后自动移除
        setTimeout(() => {
            if (resultDiv.parentElement) {
                resultDiv.remove();
            }
        }, 10000);
    }
    
    // 导出测试函数供手动调用
    window.runModuleTests = runTests;
    
})();

console.log('模块测试脚本已加载，将在DOM加载完成后自动运行测试');