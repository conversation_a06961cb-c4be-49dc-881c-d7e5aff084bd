/**
 * 导出处理模块
 * 负责处理各种数据导出功能
 */

// 确保依赖模块已加载
if (!window.TaixiangApp || !window.ExportManager) {
    throw new Error('TaixiangApp Core 和 ExportManager 模块未加载');
}

TaixiangApp.ExportHandler = {
    // 导出数据功能
    exportData: function(format) {
        console.log(`开始导出数据，格式: ${format}`);
        
        // 获取当前活动选项卡
        const activeTab = document.querySelector('.tab-pane.active');
        if (!activeTab) {
            alert('无法识别当前数据视图');
            return;
        }
        
        const tabId = activeTab.id;
        let table = activeTab.querySelector('.data-table');
        
        if (!table) {
            alert('找不到数据表格');
            return;
        }
        
        // 调用统一的导出管理模块
        ExportManager.exportData(format, {
            tabId: tabId,
            table: table
        });
    },

    // 带搜索查询的导出数据功能
    exportDataWithSearch: function(format) {
        console.log(`使用带搜索查询的导出功能，格式: ${format}`);
        
        // 获取当前活动选项卡
        const activeTab = document.querySelector('.tab-pane.active');
        if (!activeTab) {
            alert('无法识别当前数据视图');
            return;
        }
        
        const tabId = activeTab.id;
        let table = activeTab.querySelector('.data-table');
        
        if (!table) {
            alert('找不到数据表格');
            return;
        }
        
        // 获取搜索查询参数
        let searchQuery = '';
        if ($.fn.DataTable.isDataTable(table)) {
            const dataTable = $(table).DataTable();
            searchQuery = dataTable.search();
            console.log(`获取到搜索查询: ${searchQuery || '无'}`);
        }
        
        // 调用统一的导出管理模块
        ExportManager.exportData(format, {
            tabId: tabId,
            table: table,
            searchQuery: searchQuery
        });
    },

    // 导出到服务器
    exportToServer: function(format, data) {
        console.log(`开始向服务器导出数据，格式: ${format}`);
        
        if (!data || !data.length) {
            alert('没有数据可以导出');
            return;
        }
        
        // 显示加载指示器
        TaixiangApp.Utils.showLoading();
        
        // 构建请求数据
        const requestData = {
            format: format,
            data: data,
            timestamp: new Date().toISOString()
        };
        
        // 发送到服务器
        fetch('/api/export_data', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(requestData)
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('导出请求失败');
            }
            
            // 如果响应是文件，直接下载
            const contentType = response.headers.get('Content-Type');
            if (contentType && contentType.includes('application/')) {
                return response.blob().then(blob => {
                    this.downloadBlob(blob, `export_${Date.now()}.${format}`);
                });
            }
            
            // 否则解析JSON响应
            return response.json();
        })
        .then(result => {
            TaixiangApp.Utils.hideLoading();
            
            if (result && result.download_url) {
                // 如果返回下载链接，创建下载
                const link = document.createElement('a');
                link.href = result.download_url;
                link.download = result.filename || `export_${Date.now()}.${format}`;
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
                
                console.log('文件导出成功:', result.filename);
            } else {
                console.log('导出完成');
            }
        })
        .catch(error => {
            console.error('导出失败:', error);
            alert(`导出失败: ${error.message}`);
            TaixiangApp.Utils.hideLoading();
        });
    },

    // 下载Blob对象
    downloadBlob: function(blob, filename) {
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = filename;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);
    },

    // 获取当前标签页数据
    getCurrentTabData: function() {
        const activeTab = document.querySelector('.tab-pane.active');
        if (!activeTab) {
            return null;
        }
        
        const tabId = activeTab.id;
        const table = activeTab.querySelector('.data-table');
        
        if (!table) {
            return null;
        }
        
        // 从表格中提取数据
        const data = [];
        const headers = [];
        
        // 获取表头
        const headerRows = table.querySelectorAll('thead tr');
        if (headerRows.length > 0) {
            const headerCells = headerRows[0].querySelectorAll('th');
            headerCells.forEach(cell => {
                headers.push(cell.textContent.trim());
            });
        }
        
        // 获取数据行
        const dataRows = table.querySelectorAll('tbody tr');
        dataRows.forEach(row => {
            const rowData = {};
            const cells = row.querySelectorAll('td');
            
            cells.forEach((cell, index) => {
                if (index < headers.length) {
                    rowData[headers[index]] = TaixiangApp.DataDisplay.stripHtml(cell.innerHTML).trim();
                }
            });
            
            if (Object.keys(rowData).length > 0) {
                data.push(rowData);
            }
        });
        
        return {
            tabId: tabId,
            headers: headers,
            data: data
        };
    },

    // 导出为CSV格式
    exportToCSV: function() {
        const tabData = this.getCurrentTabData();
        if (!tabData || !tabData.data.length) {
            alert('没有数据可以导出');
            return;
        }
        
        let csvContent = '';
        
        // 添加表头
        csvContent += tabData.headers.join(',') + '\n';
        
        // 添加数据行
        tabData.data.forEach(row => {
            const values = tabData.headers.map(header => {
                let value = row[header] || '';
                // 处理包含逗号或引号的值
                if (value.includes(',') || value.includes('"') || value.includes('\n')) {
                    value = '"' + value.replace(/"/g, '""') + '"';
                }
                return value;
            });
            csvContent += values.join(',') + '\n';
        });
        
        // 创建下载
        const blob = new Blob(['\uFEFF' + csvContent], { type: 'text/csv;charset=utf-8;' });
        this.downloadBlob(blob, `${tabData.tabId}_export_${Date.now()}.csv`);
        
        console.log(`CSV导出完成，共 ${tabData.data.length} 条记录`);
    },

    // 导出为Excel格式（通过服务器）
    exportToExcel: function() {
        const tabData = this.getCurrentTabData();
        if (!tabData || !tabData.data.length) {
            alert('没有数据可以导出');
            return;
        }
        
        this.exportToServer('xlsx', tabData.data);
    },

    // 导出为JSON格式
    exportToJSON: function() {
        const tabData = this.getCurrentTabData();
        if (!tabData || !tabData.data.length) {
            alert('没有数据可以导出');
            return;
        }
        
        const jsonContent = JSON.stringify(tabData.data, null, 2);
        const blob = new Blob([jsonContent], { type: 'application/json;charset=utf-8;' });
        this.downloadBlob(blob, `${tabData.tabId}_export_${Date.now()}.json`);
        
        console.log(`JSON导出完成，共 ${tabData.data.length} 条记录`);
    },

    // 检查是否有可导出的数据
    hasExportableData: function() {
        const activeTab = document.querySelector('.tab-pane.active');
        if (!activeTab) {
            return false;
        }
        
        const table = activeTab.querySelector('.data-table tbody');
        if (!table) {
            return false;
        }
        
        const rows = table.querySelectorAll('tr');
        return rows.length > 0;
    },

    // 显示导出选项菜单
    showExportOptions: function() {
        if (!this.hasExportableData()) {
            alert('当前没有可导出的数据');
            return;
        }
        
        const exportOptions = `
            <div class="dropdown">
                <button class="btn btn-success dropdown-toggle" type="button" data-bs-toggle="dropdown">
                    <i class="bi bi-download"></i> 导出数据
                </button>
                <ul class="dropdown-menu">
                    <li><a class="dropdown-item" href="javascript:void(0);" onclick="TaixiangApp.ExportHandler.exportToCSV()">
                        <i class="bi bi-filetype-csv"></i> 导出为CSV
                    </a></li>
                    <li><a class="dropdown-item" href="javascript:void(0);" onclick="TaixiangApp.ExportHandler.exportToExcel()">
                        <i class="bi bi-filetype-xlsx"></i> 导出为Excel
                    </a></li>
                    <li><a class="dropdown-item" href="javascript:void(0);" onclick="TaixiangApp.ExportHandler.exportToJSON()">
                        <i class="bi bi-filetype-json"></i> 导出为JSON
                    </a></li>
                </ul>
            </div>
        `;
        
        // 可以在这里显示导出选项，或者返回HTML供其他地方使用
        return exportOptions;
    }
};

// 向后兼容的全局函数
window.exportData = TaixiangApp.ExportHandler.exportData.bind(TaixiangApp.ExportHandler);
window.exportDataWithSearch = TaixiangApp.ExportHandler.exportDataWithSearch.bind(TaixiangApp.ExportHandler);
window.exportToServer = TaixiangApp.ExportHandler.exportToServer.bind(TaixiangApp.ExportHandler);
window.exportToCSV = TaixiangApp.ExportHandler.exportToCSV.bind(TaixiangApp.ExportHandler);
window.exportToExcel = TaixiangApp.ExportHandler.exportToExcel.bind(TaixiangApp.ExportHandler);
window.exportToJSON = TaixiangApp.ExportHandler.exportToJSON.bind(TaixiangApp.ExportHandler);

console.log('导出处理模块已加载'); 