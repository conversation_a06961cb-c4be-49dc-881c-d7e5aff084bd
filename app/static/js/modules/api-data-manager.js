/**
 * API数据管理模块
 * 负责API请求、数据缓存和状态管理
 */

// 确保核心模块已加载
if (!window.TaixiangApp) {
    throw new Error('TaixiangApp Core 模块未加载，请先加载 app-core.js');
}

// API数据缓存管理器
TaixiangApp.ApiDataManager = {
    // 存储数据到sessionStorage和内存中
    storeData: function(endpoint, params, data) {
        const storageKey = this.generateStorageKey(endpoint, params);
        
        // 存储到sessionStorage中以便页面刷新后仍可使用
        sessionStorage.setItem(storageKey, JSON.stringify({
            timestamp: Date.now(),
            data: data
        }));
        
        // 同时保存到内存中以便快速访问
        if (!window.apiDataCache) {
            window.apiDataCache = {};
        }
        window.apiDataCache[storageKey] = data;
        
        console.log(`已缓存API数据: ${endpoint}`);
        return data;
    },
    
    // 确保API端点带有_db后缀
    ensureDbEndpoint: function(endpoint) {
        if (endpoint.endsWith('_db') || ['ping', 'version'].includes(endpoint)) {
            return endpoint;
        }
        return `${endpoint}_db`;
    },
    
    // 获取数据，优先从内存获取，否则从sessionStorage获取
    getData: function(endpoint, params) {
        const storageKey = this.generateStorageKey(endpoint, params);
        
        // 首先从内存缓存中检查
        if (window.apiDataCache && window.apiDataCache[storageKey]) {
            console.log(`从内存缓存获取API数据: ${endpoint}`);
            return window.apiDataCache[storageKey];
        }
        
        // 如果内存中没有，检查sessionStorage
        const storedItem = sessionStorage.getItem(storageKey);
        if (storedItem) {
            try {
                const parsedItem = JSON.parse(storedItem);
                const now = Date.now();
                const timestamp = parsedItem.timestamp;
                const maxAge = 30 * 60 * 1000; // 30分钟
                
                if (now - timestamp < maxAge) {
                    // 将数据也存入内存缓存
                    if (!window.apiDataCache) {
                        window.apiDataCache = {};
                    }
                    window.apiDataCache[storageKey] = parsedItem.data;
                    
                    console.log(`从sessionStorage获取API数据: ${endpoint}`);
                    return parsedItem.data;
                } else {
                    console.log(`API数据已过期: ${endpoint}`);
                    sessionStorage.removeItem(storageKey);
                }
            } catch (e) {
                console.error('解析存储的API数据时出错:', e);
                sessionStorage.removeItem(storageKey);
            }
        }
        
        return null;
    },
    
    // 使用API端点和参数生成唯一存储键
    generateStorageKey: function(endpoint, params) {
        let key = `api_${endpoint}`;
        
        if (params) {
            const paramKeys = Object.keys(params).sort();
            const paramString = paramKeys.map(k => `${k}=${params[k]}`).join('&');
            if (paramString) {
                key += `_${paramString}`;
            }
        }
        
        return key;
    },
    
    // 清除特定端点的缓存数据
    clearEndpointCache: function(endpoint) {
        const keyPrefix = `api_${endpoint}`;
        
        // 清除sessionStorage中的数据
        Object.keys(sessionStorage).forEach(key => {
            if (key.startsWith(keyPrefix)) {
                sessionStorage.removeItem(key);
            }
        });
        
        // 清除内存缓存中的数据
        if (window.apiDataCache) {
            Object.keys(window.apiDataCache).forEach(key => {
                if (key.startsWith(keyPrefix)) {
                    delete window.apiDataCache[key];
                }
            });
        }
        
        console.log(`已清除API缓存: ${endpoint}`);
    },
    
    // 清除所有缓存数据
    clearAllCache: function() {
        // 清除所有以api_开头的sessionStorage项
        Object.keys(sessionStorage).forEach(key => {
            if (key.startsWith('api_')) {
                sessionStorage.removeItem(key);
            }
        });
        
        // 重置内存缓存
        window.apiDataCache = {};
        
        console.log('已清除所有API缓存');
    },
    
    // 通用API请求函数
    fetchApi: function(endpoint, params = {}, method = 'GET', body = null) {
        // 确保端点带有_db后缀
        endpoint = this.ensureDbEndpoint(endpoint);

        // 构建URL和请求选项
        let url = `/api/${endpoint}`;
        const options = {
            method: method,
            headers: {
                'Content-Type': 'application/json',
            },
        };

        if (method === 'GET' && Object.keys(params).length > 0) {
            const queryParams = new URLSearchParams();
            Object.keys(params).forEach(key => {
                if (params[key] !== null && params[key] !== undefined) {
                    queryParams.append(key, params[key]);
                }
            });
            if (queryParams.toString()) {
                url += `?${queryParams.toString()}`;
            }
        } else if (method === 'POST' && body) {
            options.body = JSON.stringify(body);
        }

        console.log(`发送API请求: ${method} ${url}`);
        
        return fetch(url, options)
            .then(response => {
                if (!response.ok) {
                    throw new Error(`API请求失败: ${response.status} ${response.statusText}`);
                }
                return response.json();
            })
            .then(data => {
                console.log(`API请求成功: ${endpoint}`, data);
                return data;
            })
            .catch(error => {
                console.error(`API请求失败: ${endpoint}`, error);
                throw error;
            });
    }
};

// 向后兼容的全局对象
window.ApiDataManager = TaixiangApp.ApiDataManager;

// 向后兼容的全局函数
window.fetchApi = TaixiangApp.ApiDataManager.fetchApi.bind(TaixiangApp.ApiDataManager);

console.log('API数据管理模块已加载');