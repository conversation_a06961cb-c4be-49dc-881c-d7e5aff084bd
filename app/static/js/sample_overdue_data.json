{"results": [{"订单日期": "2023-05-15", "订单编号": "ORD20230515001", "客户姓名": "张三", "贷后状态": "逾期", "客户手机": "138****1234", "客服归属": "王客服", "业务归属": "李业务", "产品": "消费贷", "期数": 12, "总待收": 10000.0, "当前待收": 5000.0, "首次逾期期数": 3, "账单日期": "2023-08-15", "逾期天数": 45, "备注信息": "已电话联系"}, {"订单日期": "2023-06-20", "订单编号": "ORD20230620002", "客户姓名": "李四", "贷后状态": "严重逾期", "客户手机": "139****5678", "客服归属": "赵客服", "业务归属": "钱业务", "产品": "经营贷", "期数": 24, "总待收": 50000.0, "当前待收": 35000.0, "首次逾期期数": 5, "账单日期": "2023-09-20", "逾期天数": 90, "备注信息": "无法联系"}, {"订单日期": "2023-07-10", "订单编号": "ORD20230710003", "客户姓名": "王五", "贷后状态": "轻微逾期", "客户手机": "137****9012", "客服归属": "孙客服", "业务归属": "周业务", "产品": "房贷", "期数": 36, "总待收": 200000.0, "当前待收": 180000.0, "首次逾期期数": 10, "账单日期": "2023-10-10", "逾期天数": 15, "备注信息": "承诺还款"}], "columns": ["订单日期", "订单编号", "客户姓名", "贷后状态", "客户手机", "客服归属", "业务归属", "产品", "期数", "总待收", "当前待收", "首次逾期期数", "账单日期", "逾期天数", "备注信息"], "summary": {"total_count": 3, "total_amount": 220000.0, "avg_overdue_days": 50}}