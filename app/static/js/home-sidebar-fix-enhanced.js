/**
 * 首页侧边栏移动端修复器 - 增强版
 * 专门解决首页侧边栏悬浮按钮点击无反应的问题
 */

(function() {
    'use strict';
    
    // 确保只在首页运行
    if (window.location.pathname !== '/' && window.location.pathname !== '/home') {
        return;
    }
    
    console.log('🔧 首页侧边栏增强修复器：开始初始化');
    
    let initialized = false;
    let retryCount = 0;
    const maxRetries = 10;
    
    function forceInitHomeSidebarFix() {
        if (initialized) {
            console.log('✅ 首页侧边栏增强修复器：已初始化，跳过');
            return;
        }
        
        retryCount++;
        console.log(`🔍 首页侧边栏增强修复器：第${retryCount}次尝试初始化`);
        
        // 查找关键元素
        const sidebarToggle = document.getElementById('sidebarToggle');
        const sidebar = document.querySelector('.sidebar');
        const overlay = document.getElementById('sidebarOverlay');
        
        console.log('🔍 元素查找结果:', {
            sidebarToggle: !!sidebarToggle,
            sidebar: !!sidebar,
            overlay: !!overlay,
            sidebarToggleVisible: sidebarToggle ? getComputedStyle(sidebarToggle).display !== 'none' : false
        });
        
        if (!sidebarToggle || !sidebar) {
            if (retryCount < maxRetries) {
                console.log(`⏳ 首页侧边栏增强修复器：未找到必要元素，${500}ms后重试 (${retryCount}/${maxRetries})`);
                setTimeout(forceInitHomeSidebarFix, 500);
                return;
            } else {
                console.error('❌ 首页侧边栏增强修复器：达到最大重试次数，初始化失败');
                return;
            }
        }
        
        console.log('✅ 首页侧边栏增强修复器：找到必要元素，开始强化修复');
        
        // 彻底清除所有可能的事件监听器
        function clearAllEventListeners(element) {
            const newElement = element.cloneNode(true);
            element.parentNode.replaceChild(newElement, element);
            return newElement;
        }
        
        // 清除并重建切换按钮
        const newToggle = clearAllEventListeners(sidebarToggle);
        
        // 清除并重建遮罩层
        let newOverlay = null;
        if (overlay) {
            newOverlay = clearAllEventListeners(overlay);
        }
        
        console.log('🧹 首页侧边栏增强修复器：已清除所有旧事件监听器');
        
        // 强制设置按钮样式和可见性
        function forceButtonStyle() {
            const isMobile = window.innerWidth <= 768;
            
            console.log(`📱 当前屏幕模式: ${isMobile ? '移动端' : '桌面端'} (${window.innerWidth}px)`);
            
            if (isMobile) {
                // 强制设置移动端样式
                newToggle.style.cssText = `
                    display: flex !important;
                    position: fixed !important;
                    top: 10px !important;
                    left: 10px !important;
                    z-index: 1040 !important;
                    width: 40px !important;
                    height: 40px !important;
                    background-color: white !important;
                    border-radius: 50% !important;
                    box-shadow: 0 2px 5px rgba(0,0,0,0.2) !important;
                    align-items: center !important;
                    justify-content: center !important;
                    cursor: pointer !important;
                    border: 1px solid rgba(0,0,0,0.1) !important;
                    transition: all 0.2s ease !important;
                `;
                
                // 确保图标正确显示
                const icon = newToggle.querySelector('i');
                if (icon) {
                    icon.style.cssText = `
                        font-size: 20px !important;
                        color: #333 !important;
                        margin: 0 !important;
                        padding: 0 !important;
                        line-height: 1 !important;
                    `;
                }
                
                console.log('🎨 已强制设置移动端按钮样式');
            } else {
                newToggle.style.display = 'none !important';
                console.log('🖥️ 桌面端模式，隐藏移动端按钮');
            }
        }
        
        // 立即设置按钮样式
        forceButtonStyle();
        
        // 添加强化的点击事件监听器
        function handleToggleClick(e) {
            e.preventDefault();
            e.stopPropagation();
            e.stopImmediatePropagation();
            
            console.log('🖱️ 首页侧边栏增强修复器：悬浮按钮被点击');
            
            const isMobile = window.innerWidth <= 768;
            
            if (!isMobile) {
                console.log('🖥️ 当前为桌面端模式，忽略点击');
                return;
            }
            
            // 检查侧边栏当前状态
            const isActive = sidebar.classList.contains('sidebar-active');
            
            console.log(`📋 当前侧边栏状态: ${isActive ? '已打开' : '已关闭'}`);
            
            if (isActive) {
                // 关闭侧边栏
                sidebar.classList.remove('sidebar-active');
                if (newOverlay) {
                    newOverlay.classList.remove('overlay-active');
                }
                console.log('✅ 侧边栏已关闭');
            } else {
                // 打开侧边栏
                sidebar.classList.add('sidebar-active');
                if (newOverlay) {
                    newOverlay.classList.add('overlay-active');
                }
                console.log('✅ 侧边栏已打开');
            }
            
            // 添加视觉反馈
            newToggle.style.transform = 'scale(0.95)';
            setTimeout(() => {
                newToggle.style.transform = 'scale(1)';
            }, 150);
        }
        
        // 绑定多种事件类型确保响应
        const eventTypes = ['click', 'touchstart', 'touchend', 'mousedown', 'mouseup'];
        let clickHandled = false;
        
        eventTypes.forEach(eventType => {
            newToggle.addEventListener(eventType, function(e) {
                if (eventType === 'click' || eventType === 'touchend' || eventType === 'mouseup') {
                    if (!clickHandled) {
                        clickHandled = true;
                        handleToggleClick(e);
                        setTimeout(() => { clickHandled = false; }, 300);
                    }
                }
            }, { passive: false, capture: true });
        });
        
        console.log('🎯 已绑定多种事件类型到悬浮按钮');
        
        // 处理遮罩层点击事件
        if (newOverlay) {
            newOverlay.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                
                console.log('🖱️ 遮罩层被点击，关闭侧边栏');
                
                sidebar.classList.remove('sidebar-active');
                newOverlay.classList.remove('overlay-active');
            }, { passive: false, capture: true });
        }
        
        // 处理窗口大小变化
        let resizeTimer;
        function handleResize() {
            clearTimeout(resizeTimer);
            resizeTimer = setTimeout(() => {
                console.log('📐 窗口大小发生变化，重新设置按钮样式');
                forceButtonStyle();
                
                const isMobile = window.innerWidth <= 768;
                if (!isMobile) {
                    // 桌面端时关闭移动端侧边栏
                    sidebar.classList.remove('sidebar-active');
                    if (newOverlay) {
                        newOverlay.classList.remove('overlay-active');
                    }
                }
            }, 250);
        }
        
        window.addEventListener('resize', handleResize);
        window.addEventListener('orientationchange', handleResize);
        
        // 定期检查按钮状态
        function periodicCheck() {
            const isMobile = window.innerWidth <= 768;
            if (isMobile && newToggle.style.display === 'none') {
                console.log('🔄 检测到按钮被隐藏，重新设置样式');
                forceButtonStyle();
            }
        }
        
        setInterval(periodicCheck, 2000);
        
        // 添加调试信息
        window.homeSidebarDebug = {
            toggle: newToggle,
            sidebar: sidebar,
            overlay: newOverlay,
            forceButtonStyle: forceButtonStyle,
            testClick: () => handleToggleClick({ preventDefault: () => {}, stopPropagation: () => {}, stopImmediatePropagation: () => {} })
        };
        
        initialized = true;
        console.log('🎉 首页侧边栏增强修复器：初始化完成！');
        console.log('💡 调试提示：可在控制台使用 window.homeSidebarDebug 进行调试');
    }
    
    // 多种初始化时机
    function initializeAtMultipleTimes() {
        // 立即尝试
        forceInitHomeSidebarFix();
        
        // DOM加载完成后
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', forceInitHomeSidebarFix);
        }
        
        // 页面完全加载后
        window.addEventListener('load', function() {
            setTimeout(forceInitHomeSidebarFix, 100);
        });
        
        // 延迟初始化（确保所有其他脚本都已加载）
        setTimeout(forceInitHomeSidebarFix, 1000);
        setTimeout(forceInitHomeSidebarFix, 2000);
    }
    
    initializeAtMultipleTimes();
    
})(); 