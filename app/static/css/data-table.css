/* 
 * 数据表格样式
 * 优化版本 - 使用CSS变量，减少!important使用
 */

/* 表格容器 */
.table-responsive {
  border-radius: var(--border-radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
  margin-bottom: var(--spacing-lg);
  background-color: #fff;
}

/* 基础表格样式 */
.data-table {
  width: 100%;
  margin: 0;
  border-collapse: separate;
  border-spacing: 0;
  font-size: var(--font-size-sm);
}

/* 表头样式 */
.data-table thead th {
  padding: calc(var(--spacing-sm) * 1.5) var(--spacing-sm);
  background-color: var(--table-head-bg);
  color: var(--table-head-color);
  font-weight: 600;
  text-align: center;
  border-bottom: 2px solid var(--table-border-color);
  vertical-align: middle;
  position: relative;
  white-space: nowrap;
}

/* 单元格样式 */
.data-table tbody td {
  padding: var(--spacing-sm);
  border-bottom: 1px solid var(--table-border-color);
  text-align: center;
  vertical-align: middle;
  transition: background-color var(--transition-fast);
}

/* 行悬停效果 */
.data-table tbody tr:hover td {
  background-color: var(--table-hover-bg);
}

/* 斑马条纹 */
.data-table tbody tr:nth-of-type(odd) td {
  background-color: var(--table-stripe-bg);
}

/* 响应式控制列 */
.data-table td.dtr-control {
  position: relative;
  text-align: center;
  cursor: pointer;
  padding-left: 0;
  padding-right: 0;
  width: 40px;
  min-width: 40px;
}

/* 展开/折叠按钮基础样式 */
.data-table td.dtr-control:before {
  content: '+';
  background-color: #007bff;
  color: white;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  font-weight: bold;
  margin: 0 auto;
  position: relative;
  z-index: 2;
}

/* 折叠状态按钮样式 */
.data-table tr.parent td.dtr-control:before {
  content: '-';
  background-color: #dc3545;
}

/* 确保控制列中的按钮不会与内容重叠 */
table.dataTable.dtr-inline.collapsed > tbody > tr > td.dtr-control:before,
table.dataTable.dtr-inline.collapsed > tbody > tr > th.dtr-control:before {
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  margin: 0;
  box-shadow: 0 0 0.25rem rgba(0, 123, 255, 0.5);
}

/* 确保详情视图下按钮位置正确 */
table.dataTable > tbody > tr.child ul.dtr-details {
  display: block;
  list-style-type: none;
  margin: 0;
  padding: 0;
}

/* 确保控制列宽度一致 */
table.dataTable > thead > tr > th:first-child.dtr-control,
table.dataTable > tbody > tr > td:first-child.dtr-control {
  padding-left: 0;
  padding-right: 0;
  width: 40px;
  min-width: 40px;
  text-align: center;
}

/* 主要内容列 */
.data-table .main-content-cell {
  font-weight: 500;
  color: #212529;
}

/* 自动折叠的长文本单元格 */
.cell-content {
  position: relative;
  max-width: 180px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: left;
  display: inline-block;
}

.cell-content.expanded {
  white-space: normal;
  max-width: 100%;
  overflow: visible;
  word-wrap: break-word;
  word-break: break-word;
  overflow-wrap: break-word;
  background-color: #f8f9fa;
  padding: 4px 8px;
  border-radius: 4px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  z-index: 10;
  position: relative;
}

.cell-content:hover:not(.expanded) {
  color: #007bff;
}

/* 展开图标 */
.cell-content:after {
  content: '';
  display: inline-block;
  width: 14px;
  height: 14px;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23007bff'%3E%3Cpath d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-size: contain;
  vertical-align: middle;
  margin-left: 4px;
  opacity: 0.6;
  transition: transform 0.2s ease;
}

.cell-content.expanded:after {
  transform: rotate(180deg);
  opacity: 1;
}

/* 备注列样式 */
td.remarks-cell {
  text-align: left !important;
  max-width: 200px;
}

/* 重要数据样式 */
.important-data {
  font-weight: 500;
  color: #0d6efd;
}

/* 响应式详情样式 */
.dtr-details {
  width: 100%;
  margin-bottom: 0;
  background-color: #f8f9fa;
  border-radius: 4px;
}

.dtr-details li {
  display: flex;
  padding: 8px 12px;
  border-bottom: 1px solid #eee;
}

.dtr-details li:last-child {
  border-bottom: none;
}

.dtr-title {
  font-weight: 500;
  color: #495057;
  min-width: 120px;
  padding-right: 10px;
}

.dtr-data {
  flex: 1;
  text-align: left;
  word-break: break-word;
}

/* 分页组件样式重构 */
.dataTables_wrapper .dataTables_paginate {
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 15px 0;
  padding: 8px 4px;
  font-size: 14px;
  border-radius: 6px;
  overflow-x: auto;
  white-space: nowrap;
  -ms-overflow-style: none;  /* IE and Edge */
  scrollbar-width: none;  /* Firefox */
  position: relative;
  background-color: rgba(248, 249, 250, 0.5);
  transition: all 0.25s ease;
}

/* 隐藏滚动条但保持可滚动 */
.dataTables_wrapper .dataTables_paginate::-webkit-scrollbar {
  display: none; /* Chrome, Safari and Opera */
}

/* 滚动指示器优化 */
.dataTables_wrapper .dataTables_paginate.pagination-scrollable {
  position: relative;
}

.dataTables_wrapper .dataTables_paginate.pagination-scrollable:before,
.dataTables_wrapper .dataTables_paginate.pagination-scrollable:after {
  content: "";
  position: absolute;
  top: 0;
  bottom: 0;
  width: 18px;
  pointer-events: none;
  transition: opacity 0.3s ease;
  opacity: 0;
  z-index: 2;
}

.dataTables_wrapper .dataTables_paginate.pagination-scrollable:before {
  left: 0;
  background: linear-gradient(to right, rgba(248, 249, 250, 0.95) 0%, rgba(248, 249, 250, 0) 100%);
}

.dataTables_wrapper .dataTables_paginate.pagination-scrollable:after {
  right: 0;
  background: linear-gradient(to left, rgba(248, 249, 250, 0.95) 0%, rgba(248, 249, 250, 0) 100%);
}

.dataTables_wrapper .dataTables_paginate.pagination-scrollable:not(.at-left-edge):before {
  opacity: 1;
}

.dataTables_wrapper .dataTables_paginate.pagination-scrollable:not(.at-right-edge):after {
  opacity: 1;
}

/* 分页按钮样式优化 */
.dataTables_wrapper .dataTables_paginate .paginate_button {
  display: inline-flex;
  justify-content: center;
  align-items: center;
  padding: 0;
  margin: 0 3px;
  border: 1px solid #e0e0e0;
  color: #505050 !important;
  background-color: #fff;
  font-size: 14px;
  min-width: 36px;
  min-height: 36px;
  box-shadow: 0 1px 2px rgba(0,0,0,0.04);
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none !important;
}

.dataTables_wrapper .dataTables_paginate .paginate_button:hover {
  background-color: #f8f9fa;
  border-color: #c0c0c0;
  color: #007bff !important;
  box-shadow: 0 2px 4px rgba(0,0,0,0.08);
  transform: translateY(-1px);
}

/* 当前页按钮样式 */
.dataTables_wrapper .dataTables_paginate .paginate_button.current {
  background: linear-gradient(145deg, #007bff, #0062cc);
  color: white !important;
  border-color: #0062cc;
  font-weight: 600;
  box-shadow: 0 2px 5px rgba(0,123,255,0.3);
  position: relative;
  z-index: 1;
}

.dataTables_wrapper .dataTables_paginate .paginate_button.current:hover {
  background: linear-gradient(145deg, #0069d9, #0056b3);
  transform: translateY(-1px);
  box-shadow: 0 3px 6px rgba(0,123,255,0.4);
}

/* 禁用按钮样式 */
.dataTables_wrapper .dataTables_paginate .paginate_button.disabled {
  color: #c0c0c0 !important;
  background-color: #f8f8f8;
  border-color: #ebebeb;
  cursor: not-allowed;
  box-shadow: none;
  opacity: 0.7;
}

.dataTables_wrapper .dataTables_paginate .paginate_button.disabled:hover {
  background-color: #f8f8f8;
  border-color: #ebebeb;
  color: #c0c0c0 !important;
  transform: none;
  box-shadow: none;
}

/* 首页/末页按钮样式 */
.dataTables_wrapper .dataTables_paginate .paginate_button.first,
.dataTables_wrapper .dataTables_paginate .paginate_button.last {
  font-weight: 500;
}

/* 前一页/后一页按钮样式 */
.dataTables_wrapper .dataTables_paginate .paginate_button.previous,
.dataTables_wrapper .dataTables_paginate .paginate_button.next {
  min-width: 40px;
  position: relative;
  font-weight: 500;
}

/* 页码信息显示优化 */
.dataTables_wrapper .dataTables_info {
  padding-top: 12px;
  color: #666;
  text-align: center;
  font-size: 13px;
}

/* 页数跳转输入框 */
.dataTables_wrapper .dataTables_paginate .paginate_input {
  width: 50px;
  padding: 5px;
  margin: 0 5px;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  text-align: center;
  font-size: 14px;
}

.dataTables_wrapper .dataTables_paginate .paginate_of {
  margin: 0 5px;
  color: #666;
}

/* 移动端优化 */
@media (max-width: 768px) {
  .data-table thead th,
  .data-table tbody td {
    padding: 8px 6px;
    font-size: 13px;
  }
  
  .cell-content {
    max-width: 120px;
  }
  
  .dataTables_wrapper .dataTables_filter input {
    min-width: 150px;
  }
  
  .dataTables_wrapper .dataTables_paginate {
    overflow-x: auto;
    white-space: nowrap;
    width: 100%;
    padding: 5px 0;
    justify-content: flex-start;  /* 从左侧开始排列 */
    -webkit-overflow-scrolling: touch;
  }
  
  .dataTables_wrapper .dataTables_paginate .paginate_button {
    padding: 5px 10px;
    margin: 0 2px;
  }
  
  .dtr-details li {
    padding: 6px 8px;
  }
  
  .dtr-title {
    min-width: 100px;
  }
  
  /* 修复移动端折叠按钮未垂直居中问题 */
  table.dataTable.dtr-inline.collapsed > tbody > tr > td.dtr-control:before,
  table.dataTable.dtr-inline.collapsed > tbody > tr > th.dtr-control:before {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    margin: 0;
    box-shadow: 0 0 0.25rem rgba(0, 123, 255, 0.5);
  }
  
  td.dtr-control {
    position: relative;
  }
}

@media (max-width: 480px) {
  .data-table thead th,
  .data-table tbody td {
    padding: 6px 4px;
    font-size: 12px;
  }
  
  .cell-content {
    max-width: 100px;
  }
  
  /* 使用可滚动的分页导航代替自动换行 */
  .dataTables_wrapper .dataTables_paginate {
    padding: 8px 0;
    overflow-x: auto;
    white-space: nowrap;
    width: 100%;
    display: block;
    text-align: center;
    -webkit-overflow-scrolling: touch;
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */
    position: relative; /* 为滚动指示器添加定位上下文 */
  }
  
  /* 创建滚动阴影指示器 */
  .dataTables_wrapper .dataTables_paginate:before,
  .dataTables_wrapper .dataTables_paginate:after {
    content: "";
    position: absolute;
    top: 0;
    bottom: 0;
    width: 20px;
    pointer-events: none;
    z-index: 1;
    opacity: 0;
    transition: opacity 0.3s;
  }
  
  .dataTables_wrapper .dataTables_paginate:before {
    left: 0;
    background: linear-gradient(to right, rgba(255,255,255,0.9), rgba(255,255,255,0));
  }
  
  .dataTables_wrapper .dataTables_paginate:after {
    right: 0;
    background: linear-gradient(to left, rgba(255,255,255,0.9), rgba(255,255,255,0));
  }
  
  .dataTables_wrapper .dataTables_paginate.pagination-scrollable:not(.at-left-edge):before {
    opacity: 1;
  }
  
  .dataTables_wrapper .dataTables_paginate.pagination-scrollable:not(.at-right-edge):after {
    opacity: 1;
  }
  
  /* 隐藏滚动条但保持可滚动 */
  .dataTables_wrapper .dataTables_paginate::-webkit-scrollbar {
    display: none; /* Chrome, Safari and Opera */
  }
  
  .dataTables_wrapper .dataTables_paginate .paginate_button {
    display: inline-block;
    padding: 8px 12px;
    margin: 0 2px;
    font-size: 14px;
    min-width: auto;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    border-radius: 4px;
  }
  
  /* 确保当前页按钮足够明显 */
  .dataTables_wrapper .dataTables_paginate .paginate_button.current {
    background-color: #007bff;
    color: white !important;
    font-weight: bold;
    box-shadow: 0 2px 5px rgba(0,123,255,0.3);
  }
  
  /* 确保分页容器不会溢出 */
  .dataTable-bottom {
    width: 100%;
    overflow: hidden;
    padding-bottom: 5px;
  }
  
  /* 添加分页滚动提示 */
  .pagination-scroll-hint {
    text-align: center;
    color: #6c757d;
    font-size: 11px;
    margin-top: 5px;
    opacity: 0.7;
  }
  
  /* 分页信息文本适配 */
  .dataTables_wrapper .dataTables_info {
    font-size: 11px;
    padding: 5px 0;
    width: 100%;
    text-align: center;
  }
}

@media (max-width: 576px) {
  .data-table thead th,
  .data-table tbody td {
    padding: 6px 4px;
    font-size: 12px;
  }
  
  .cell-content {
    max-width: 100px;
  }
  
  /* 使用可滚动的分页导航代替自动换行 */
  .dataTables_wrapper .dataTables_paginate {
    padding: 8px 0;
    overflow-x: auto;
    white-space: nowrap;
    width: 100%;
    display: block;
    text-align: center;
    -webkit-overflow-scrolling: touch;
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */
    position: relative; /* 为滚动指示器添加定位上下文 */
  }
  
  /* 创建滚动阴影指示器 */
  .dataTables_wrapper .dataTables_paginate:before,
  .dataTables_wrapper .dataTables_paginate:after {
    content: "";
    position: absolute;
    top: 0;
    bottom: 0;
    width: 20px;
    pointer-events: none;
    z-index: 1;
    opacity: 0;
    transition: opacity 0.3s;
  }
  
  .dataTables_wrapper .dataTables_paginate:before {
    left: 0;
    background: linear-gradient(to right, rgba(255,255,255,0.9), rgba(255,255,255,0));
  }
  
  .dataTables_wrapper .dataTables_paginate:after {
    right: 0;
    background: linear-gradient(to left, rgba(255,255,255,0.9), rgba(255,255,255,0));
  }
  
  .dataTables_wrapper .dataTables_paginate.pagination-scrollable:not(.at-left-edge):before {
    opacity: 1;
  }
  
  .dataTables_wrapper .dataTables_paginate.pagination-scrollable:not(.at-right-edge):after {
    opacity: 1;
  }
  
  /* 隐藏滚动条但保持可滚动 */
  .dataTables_wrapper .dataTables_paginate::-webkit-scrollbar {
    display: none; /* Chrome, Safari and Opera */
  }
  
  .dataTables_wrapper .dataTables_paginate .paginate_button {
    display: inline-block;
    padding: 8px 12px;
    margin: 0 2px;
    font-size: 14px;
    min-width: auto;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    border-radius: 4px;
  }
  
  /* 确保当前页按钮足够明显 */
  .dataTables_wrapper .dataTables_paginate .paginate_button.current {
    background-color: #007bff;
    color: white !important;
    font-weight: bold;
    box-shadow: 0 2px 5px rgba(0,123,255,0.3);
  }
  
  /* 确保分页容器不会溢出 */
  .dataTable-bottom {
    width: 100%;
    overflow: hidden;
    padding-bottom: 5px;
  }
  
  /* 添加分页滚动提示 */
  .pagination-scroll-hint {
    text-align: center;
    color: #6c757d;
    font-size: 11px;
    margin-top: 5px;
    opacity: 0.7;
  }
  
  /* 分页信息文本适配 */
  .dataTables_wrapper .dataTables_info {
    font-size: 11px;
    padding: 5px 0;
    width: 100%;
    text-align: center;
  }
}

@media (max-width: 375px) {
  .dataTables_wrapper .dataTables_paginate .paginate_button {
    padding: 5px 7px;
    min-width: 30px;
    min-height: 30px;
    font-size: 12px;
  }
}

/* 移动端分页优化 */
@media (max-width: 767.98px) {
  /* 简化分页器样式 */
  .dataTables_wrapper .dataTables_paginate {
    text-align: center;
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    padding: 8px 0;
    overflow: visible; /* 防止溢出问题 */
  }
  
  /* 页码指示器样式 */
  .current-page-indicator {
    display: inline-flex;
    padding: 6px 10px;
    background-color: #f5f5f5;
    border-radius: 4px;
    margin: 0 5px;
    font-weight: 500;
    min-width: 60px;
    justify-content: center;
  }
  
  /* 省略号样式 */
  .ellipsis-indicator {
    display: inline-flex;
    justify-content: center;
    align-items: center;
    width: 30px;
    color: #777;
  }
  
  /* 隐藏部分页码按钮 */
  .dataTables_wrapper .dataTables_paginate .paginate_button.mobile-hidden {
    display: none !important;
  }
}

/* 超小屏幕时更紧凑的分页 */
@media (max-width: 480px) {
  .dataTables_wrapper .dataTables_paginate .paginate_button {
    min-width: 32px;
    min-height: 32px;
    padding: 5px;
    margin: 0 1px;
  }
  
  /* 超小屏幕时只显示前后页按钮和页码指示器 */
  .dataTables_wrapper .dataTables_paginate .paginate_button:not(.previous):not(.next):not(.current-page-indicator) {
    display: none !important;
  }
}

/* 表格信息显示 */
.dataTables_wrapper .dataTables_info {
  padding: 10px 0;
  color: #6c757d;
  text-align: center;
}

/* 搜索框样式 */
.dataTables_wrapper .dataTables_filter {
  margin-bottom: 15px;
  text-align: right;
}

.dataTables_wrapper .dataTables_filter input {
  padding: 6px 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  min-width: 200px;
}

/* 行长度选择器 */
.dataTables_wrapper .dataTables_length {
  margin-bottom: 15px;
}

.dataTables_wrapper .dataTables_length select {
  padding: 6px 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  margin: 0 5px;
}

/* 数据表格统一样式 */

/* 表格状态样式 */
.status-account-day, .status-账单日 {
    background-color: #fff3cd !important;
    color: #856404 !important;
}

.status-overdue-unpaid, .status-逾期未还 {
    background-color: #f8d7da !important;
    color: #721c24 !important;
}

.status-overdue-paid, .status-逾期还款 {
    background-color: #fff3cd !important;
    color: #856404 !important;
}

.status-early-payment, .status-提前还款 {
    background-color: #cce5ff !important;
    color: #004085 !important;
}

.status-on-time-payment, .status-按时还款 {
    background-color: #d4edda !important;
    color: #155724 !important;
}

.status-serious-overdue, .status-严重逾期 {
    background-color: #dc3545 !important;
    color: #fff !important;
}

.status-in-collection, .status-催收中 {
    background-color: #fff3cd !important;
    color: #856404 !important;
}

.status-litigated, .status-已诉讼 {
    background-color: #f8d7da !important;
    color: #721c24 !important;
}

.status-settled, .status-已结清 {
    background-color: #d4edda !important;
    color: #155724 !important;
}

.status-cancelled, .status-已取消 {
    background-color: #6c757d !important;
    color: #fff !important;
}

.status-normal, .status-正常 {
    background-color: #d4edda !important;
    color: #155724 !important;
}

/* 统一单元格内容样式 */
.data-table td {
    vertical-align: middle;
    padding: 0.75rem;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

/* 日期单元格样式 */
.data-table .badge {
    display: inline-block;
    padding: 0.25em 0.5em;
    font-size: 0.85em;
    font-weight: 500;
    border-radius: 0.25rem;
    white-space: nowrap;
}

/* 备注内容样式 */
.remarks-content {
    max-width: 300px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.remarks-content.expanded {
    white-space: normal;
    word-break: break-word;
}

/* 客户信息高亮 */
.data-table tr:hover {
    background-color: rgba(0, 123, 255, 0.05) !important;
}

/* 表格排序指示器样式 */
.data-table thead th.sorting::after {
    opacity: 0.4;
}

.data-table thead th.sorting_asc::after,
.data-table thead th.sorting_desc::after {
    opacity: 1;
}

/* 表格头部样式统一 */
.data-table thead th {
    background-color: #f8f9fa;
    color: #495057;
    font-weight: 600;
    border-bottom: 2px solid rgba(0, 0, 0, 0.1);
    padding: 0.75rem;
    text-align: left;
    white-space: nowrap;
}

/* 响应式表格样式 */
.table-responsive {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
    margin-bottom: 1rem;
}

/* 表格翻页控件统一样式 */
.dataTables_paginate .paginate_button {
    margin: 0 0.2rem;
    padding: 0.375rem 0.75rem;
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 0.25rem;
    color: #007bff !important;
    cursor: pointer;
}

.dataTables_paginate .paginate_button:hover {
    background-color: #e9ecef;
    border-color: #dee2e6;
    color: #0056b3 !important;
}

.dataTables_paginate .paginate_button.current {
    background-color: #007bff;
    border-color: #007bff;
    color: #fff !important;
}

.dataTables_paginate .paginate_button.disabled {
    color: #6c757d !important;
    pointer-events: none;
    opacity: 0.65;
}

/* 搜索框统一样式 */
.dataTables_filter input {
    padding: 0.375rem 0.75rem;
    font-size: 0.9rem;
    line-height: 1.5;
    color: #495057;
    background-color: #fff;
    border: 1px solid #ced4da;
    border-radius: 0.25rem;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.dataTables_filter input:focus {
    color: #495057;
    background-color: #fff;
    border-color: #80bdff;
    outline: 0;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* 移动响应优化 */
@media (max-width: 767.98px) {
    .dataTables_wrapper .dataTables_length,
    .dataTables_wrapper .dataTables_filter,
    .dataTables_wrapper .dataTables_info,
    .dataTables_wrapper .dataTables_paginate {
        text-align: left;
        margin-bottom: 0.5rem;
    }
    
    .dataTables_wrapper .dataTables_filter {
        margin-top: 0.5rem;
    }
    
    .dataTables_paginate .paginate_button {
        padding: 0.25rem 0.5rem;
        font-size: 0.875rem;
    }
}

/* 统一三个标签页内容样式 */
#dataTabs .tab-pane {
    padding: 1rem 0;
}

#dataTabs .alert {
    margin-bottom: 1rem;
}

#dataTabs .nav-link {
    color: #495057;
    background-color: transparent;
    border-color: transparent transparent #dee2e6;
    cursor: pointer;
}

#dataTabs .nav-link:hover {
    color: #007bff;
    border-color: transparent transparent #dee2e6;
}

#dataTabs .nav-link.active {
    color: #007bff;
    background-color: #fff;
    border-color: #dee2e6 #dee2e6 #fff;
    font-weight: 600;
}

/* 确保所有标签页有相同的内边距和结构 */
#dataTabs .tab-content > .tab-pane {
    min-height: 300px;
}

/* 统一数据展示区域样式 */
.data-display-area {
    background-color: #fff;
    border-radius: 0.25rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

/* 确保表格具有一致的高度和滚动行为 */
.data-table-container {
    max-height: 600px;
    overflow-y: auto;
}

/* 保持表格头固定 */
.data-table thead {
    position: sticky;
    top: 0;
    z-index: 1;
}
