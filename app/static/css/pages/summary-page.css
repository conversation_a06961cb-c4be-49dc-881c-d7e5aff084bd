/**
 * 汇总页面核心样式模块
 * 提取自summary.html内联样式，保持完全一致的视觉效果
 */

/* ==========================================================================
   动画效果模块
   ========================================================================== */

/* 自动消失提示样式 */
.auto-dismiss-alert {
    animation: fadeOut 0.5s ease 3s forwards;
}

@keyframes fadeOut {
    from { 
        opacity: 1; 
    }
    to { 
        opacity: 0; 
        height: 0; 
        margin: 0; 
        padding: 0; 
    }
}

/* ==========================================================================
   表格样式增强模块
   ========================================================================== */

/* 表格基础样式 */
.data-table {
    border-collapse: collapse;
    width: 100% !important;
    margin-bottom: 1rem;
}

.data-table th {
    background-color: #f2f2f2;
    font-weight: bold;
    text-align: left;
    padding: 10px;
    white-space: nowrap;
}

.data-table td {
    padding: 8px;
    vertical-align: middle;
}

/* 表格交替行颜色 */
.data-table tbody tr:nth-child(odd) {
    background-color: #f9f9f9;
}

/* 鼠标悬停高亮效果 */
.data-table tbody tr:hover {
    background-color: #e9f2f9;
}

/* ==========================================================================
   图表样式模块
   ========================================================================== */

/* 图表容器样式 */
.chart-container {
    height: 300px;
    position: relative;
}

/* ==========================================================================
   响应式表格模块
   ========================================================================== */

/* 响应式表格样式 */
.table-responsive {
    width: 100%;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
}

/* ==========================================================================
   DataTables响应式样式增强模块
   ========================================================================== */

/* DataTables响应式折叠控制按钮 */
table.dataTable.dtr-inline.collapsed > tbody > tr > td.dtr-control:before,
table.dataTable.dtr-inline.collapsed > tbody > tr > th.dtr-control:before {
    background-color: #007bff;
}

/* 响应式详情数据样式 */
.dtr-data {
    word-break: break-word;
}

/* 详情行样式 */
.dtr-details {
    width: 100%;
}

.dtr-details td {
    padding: 5px 10px;
}

/* ==========================================================================
   数据单元格折叠样式模块
   ========================================================================== */

/* 数据单元格折叠样式 */
.cell-content {
    position: relative;
    max-width: 150px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    cursor: pointer;
    display: block;
    transition: all 0.3s ease;
    color: #333;
}

/* 渐变遮罩效果 */
.cell-content:after {
    content: '';
    position: absolute;
    right: 0;
    top: 0;
    height: 100%;
    width: 30px;
    background: linear-gradient(to right, rgba(255,255,255,0), rgba(255,255,255,1));
    pointer-events: none;
}

/* 展开状态样式 */
.cell-content.expanded {
    white-space: normal;
    word-break: break-word;
    max-width: none;
    overflow: visible;
}

.cell-content.expanded:after {
    display: none;
}

/* 鼠标悬停状态 */
.cell-content:hover {
    color: #007bff;
}

/* ==========================================================================
   日期选择器样式模块
   ========================================================================== */

/* 日期选择器基础样式 */
input[type="date"] {
    position: relative;
}

/* 自定义日期选择器图标 */
input[type="date"]::-webkit-calendar-picker-indicator {
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
    opacity: 1;
    cursor: pointer;
}

/* 日期选择器位置调整 */
input[type="date"]::-webkit-datetime-edit {
    padding-right: 28px; /* 为图标留出空间 */
}

/* 调整弹出日历的位置 */
::-webkit-calendar-picker-indicator {
    position: relative; 
}

/* 兼容性处理 */
input[type="date"]::-webkit-inner-spin-button {
    display: none;
}

input[type="date"]::-webkit-clear-button {
    display: none;
}

/* ==========================================================================
   移动端响应式样式模块
   ========================================================================== */

/* 移动端样式优化 */
@media (max-width: 768px) {
    /* 单元格内容移动端调整 */
    .cell-content {
        max-width: 120px;
    }
    
    /* DataTables移动端响应式增强 */
    table.dataTable.dtr-inline.collapsed > tbody > tr > td.dtr-control:before,
    table.dataTable.dtr-inline.collapsed > tbody > tr > th.dtr-control:before {
        top: 50%;
        transform: translateY(-50%);
        background-color: #007bff;
        box-shadow: 0 0 0.2rem rgba(0, 123, 255, 0.5);
    }
    
    /* 响应式数据单元格 */
    .dtr-data {
        word-break: break-word;
        max-width: calc(100vw - 100px);
    }
    
    /* 移动端表格响应式 */
    .table-responsive {
        max-width: 100%;
    }
    
    /* 移动端表格单元格样式 */
    .data-table th, .data-table td {
        padding: 6px;
        font-size: 0.85rem;
    }
    
    /* 移动端图表容器 */
    .chart-container {
        height: 250px;
    }
    
    /* 移动端卡片内容 */
    .card-body {
        padding: 0.75rem;
    }
}