import json
import logging
import datetime

logger = logging.getLogger(__name__)


def prepare_order_summary_chart_data(data):
    """
    准备订单汇总图表数据
    
    Args:
        data: 订单汇总数据
    
    Returns:
        用于前端Chart.js的JSON数据
    """
    try:
        if not data or 'error' in data:
            return json.dumps({
                'labels': [],
                'datasets': []
            })
        
        # 提取月份和订单数量
        months = []
        ecommerce_values = []
        leasing_values = []
        
        # 处理不同格式的数据
        if isinstance(data, list):
            for item in data:
                if isinstance(item, dict) and 'month' in item:
                    months.append(item.get('month', ''))
                    ecommerce_values.append(item.get('ecommerce_count', 0))
                    leasing_values.append(item.get('leasing_count', 0))
        elif isinstance(data, dict) and 'results' in data and isinstance(data['results'], list):
            for item in data['results']:
                if isinstance(item, dict) and 'month' in item:
                    months.append(item.get('month', ''))
                    ecommerce_values.append(item.get('ecommerce_count', 0))
                    leasing_values.append(item.get('leasing_count', 0))
        
        # 构建Chart.js数据格式
        chart_data = {
            'labels': months,
            'datasets': [
                {
                    'label': '电商订单数量',
                    'backgroundColor': 'rgba(54, 162, 235, 0.5)',
                    'borderColor': 'rgb(54, 162, 235)',
                    'borderWidth': 1,
                    'data': ecommerce_values
                },
                {
                    'label': '租赁订单数量',
                    'backgroundColor': 'rgba(255, 99, 132, 0.5)',
                    'borderColor': 'rgb(255, 99, 132)',
                    'borderWidth': 1,
                    'data': leasing_values
                }
            ]
        }
        
        return json.dumps(chart_data)
    
    except Exception as e:
        logger.exception(f"准备订单汇总图表数据时出错: {e}")
        return json.dumps({
            'labels': [],
            'datasets': []
        })


def prepare_overdue_summary_chart_data(data):
    """
    准备逾期汇总图表数据
    
    Args:
        data: 逾期汇总数据
    
    Returns:
        用于前端Chart.js的JSON数据
    """
    try:
        if not data or 'error' in data:
            return json.dumps({
                'labels': [],
                'datasets': []
            })
        
        # 提取月份和逾期情况
        months = []
        overdue_counts = []
        overdue_amounts = []
        
        # 处理不同格式的数据
        if isinstance(data, list):
            for item in data:
                if isinstance(item, dict) and 'month' in item:
                    months.append(item.get('month', ''))
                    overdue_counts.append(item.get('overdue_count', 0))
                    overdue_amounts.append(item.get('overdue_amount', 0))
        elif isinstance(data, dict) and 'results' in data and isinstance(data['results'], list):
            for item in data['results']:
                if isinstance(item, dict) and 'month' in item:
                    months.append(item.get('month', ''))
                    overdue_counts.append(item.get('overdue_count', 0))
                    overdue_amounts.append(item.get('overdue_amount', 0))
        
        # 构建Chart.js数据格式
        chart_data = {
            'labels': months,
            'datasets': [
                {
                    'label': '逾期订单数',
                    'backgroundColor': 'rgba(255, 159, 64, 0.5)',
                    'borderColor': 'rgb(255, 159, 64)',
                    'borderWidth': 1,
                    'yAxisID': 'y',
                    'data': overdue_counts
                },
                {
                    'label': '逾期金额',
                    'backgroundColor': 'rgba(153, 102, 255, 0.5)',
                    'borderColor': 'rgb(153, 102, 255)',
                    'borderWidth': 1,
                    'yAxisID': 'y1',
                    'type': 'line',
                    'data': overdue_amounts
                }
            ]
        }
        
        return json.dumps(chart_data)
    
    except Exception as e:
        logger.exception(f"准备逾期汇总图表数据时出错: {e}")
        return json.dumps({
            'labels': [],
            'datasets': []
        })


def prepare_customer_summary_chart_data(data):
    """
    准备客户订单汇总图表数据
    
    Args:
        data: 客户订单汇总数据
    
    Returns:
        用于前端Chart.js的JSON数据
    """
    try:
        if not data or 'error' in data:
            return {
                'order_chart': json.dumps({
                    'labels': [],
                    'datasets': []
                }),
                'payment_chart': json.dumps({
                    'labels': [],
                    'datasets': []
                })
            }
        
        # 从数据中提取需要的信息
        order_info = []
        payment_status = []
        
        # 处理订单详情
        if isinstance(data, dict) and 'order_details' in data:
            order_info = data['order_details']
        
        # 提取待收明细
        if isinstance(data, dict) and 'receivable_by_periods' in data:
            payment_status = data['receivable_by_periods']
        
        # 订单详情图表数据
        order_labels = []
        order_amounts = []
        
        for item in order_info:
            if isinstance(item, dict):
                if 'order_number' in item:
                    order_labels.append(item.get('order_number', ''))
                elif 'order_id' in item:
                    order_labels.append(item.get('order_id', ''))
                
                if 'total_finance' in item:
                    order_amounts.append(item.get('total_finance', 0))
                elif 'total_amount' in item:
                    order_amounts.append(item.get('total_amount', 0))
        
        order_chart_data = {
            'labels': order_labels,
            'datasets': [
                {
                    'label': '订单金额',
                    'backgroundColor': [
                        'rgba(255, 99, 132, 0.5)',
                        'rgba(54, 162, 235, 0.5)',
                        'rgba(255, 206, 86, 0.5)',
                        'rgba(75, 192, 192, 0.5)',
                        'rgba(153, 102, 255, 0.5)',
                    ],
                    'borderColor': [
                        'rgb(255, 99, 132)',
                        'rgb(54, 162, 235)',
                        'rgb(255, 206, 86)',
                        'rgb(75, 192, 192)',
                        'rgb(153, 102, 255)',
                    ],
                    'borderWidth': 1,
                    'data': order_amounts
                }
            ]
        }
        
        # 待收明细图表数据
        period_labels = []
        period_amounts = []
        
        for item in payment_status:
            if isinstance(item, dict):
                if 'period' in item:
                    period_labels.append(f"第{item.get('period', '?')}期")
                elif 'periods' in item:
                    period_labels.append(f"第{item.get('periods', '?')}期")
                
                period_amounts.append(item.get('amount', 0))
        
        payment_chart_data = {
            'labels': period_labels,
            'datasets': [
                {
                    'label': '待收金额',
                    'backgroundColor': 'rgba(75, 192, 192, 0.5)',
                    'borderColor': 'rgb(75, 192, 192)',
                    'borderWidth': 1,
                    'data': period_amounts
                }
            ]
        }
        
        # 返回两个图表的数据
        return {
            'order_chart': json.dumps(order_chart_data),
            'payment_chart': json.dumps(payment_chart_data)
        }
    
    except Exception as e:
        logger.exception(f"准备客户订单汇总图表数据时出错: {e}")
        return {
            'order_chart': json.dumps({
                'labels': [],
                'datasets': []
            }),
            'payment_chart': json.dumps({
                'labels': [],
                'datasets': []
            })
        }


def prepare_enterprise_customer_summary_chart_data(data):
    """
    准备企业级客户订单汇总图表数据
    为企业级客户汇总页面生成三个图表的数据：
    1. 订单金额分布（按期数）
    2. 待收金额分布（按期数趋势）
    3. 业务类型分布（饼图）
    
    Args:
        data: 客户订单汇总数据
    
    Returns:
        包含三个图表数据的字典
    """
    try:
        logger.info("开始准备企业级客户汇总图表数据")
        
        if not data or 'error' in data:
            logger.warning("数据为空或包含错误，返回空图表数据")
            return generate_empty_enterprise_chart_data()
        
        # 解析数据结构
        order_details = data.get('order_details', [])
        receivable_periods = data.get('receivable_by_periods', [])
        basic_info = data.get('basic_info', {})
        order_summary = data.get('order_summary', {})
        
        logger.info(f"数据解析完成: 订单详情{len(order_details)}条, "
                    f"待收期数{len(receivable_periods)}条")
         
        # 1. 生成订单金额分布数据（按期数汇总）
        order_chart_data = generate_order_amount_chart_data(
            order_details, receivable_periods)
        
        # 2. 生成待收金额分布数据（趋势图）
        receivable_chart_data = generate_receivable_amount_chart_data(
            receivable_periods
        )
        
        # 3. 生成业务类型分布数据（饼图）
        business_chart_data = generate_business_type_chart_data(
            order_details, order_summary
        )
        
        result = {
            'orderAmounts': order_chart_data['amounts'],
            'receivableAmounts': receivable_chart_data['amounts'],
            'businessTypes': business_chart_data['types'],
            'periods': order_chart_data['periods'],
            'metadata': {
                'total_orders': len(order_details),
                'total_periods': len(receivable_periods),
                'customer_name': basic_info.get('customer_name', ''),
                'last_updated': datetime.datetime.now().isoformat()
            }
        }
        
        logger.info("企业级客户汇总图表数据准备完成")
        return result
        
    except Exception as e:
        logger.exception(f"准备企业级客户汇总图表数据时出错: {e}")
        return generate_empty_enterprise_chart_data()


def generate_order_amount_chart_data(order_details, receivable_periods):
    """
    生成订单金额分布图表数据
    按期数汇总订单金额
    """
    try:
        # 按期数汇总订单金额
        period_amounts = {}
        period_labels = []
        
        # 从待收期数数据获取期数信息
        for period_item in receivable_periods:
            if isinstance(period_item, dict):
                period = period_item.get('period', 0)
                amount = period_item.get('amount', 0)
                
                # 清理金额数据（移除货币符号和逗号）
                if isinstance(amount, str):
                    amount = clean_amount_value(amount)
                
                period_key = f"第{period}期"
                period_amounts[period_key] = float(amount) if amount else 0
                
                if period_key not in period_labels:
                    period_labels.append(period_key)
        
        # 如果没有期数数据，尝试从订单详情推算
        if not period_amounts and order_details:
            logger.info("从订单详情推算期数金额分布")
            for order in order_details:
                if isinstance(order, dict):
                    total_periods = order.get('total_periods', 0)
                    current_receivable = order.get('current_receivable', 0)
                    
                    if isinstance(current_receivable, str):
                        current_receivable = clean_amount_value(current_receivable)
                    
                    # 简单分配：假设平均分布
                    if total_periods > 0 and current_receivable > 0:
                        avg_amount = float(current_receivable) / total_periods
                        for i in range(1, total_periods + 1):
                            period_key = f"第{i}期"
                            if period_key not in period_amounts:
                                period_amounts[period_key] = avg_amount
                                if period_key not in period_labels:
                                    period_labels.append(period_key)
        
        # 确保至少有一些数据
        if not period_amounts:
            logger.warning("无法提取期数金额数据，使用默认数据")
            period_labels = ['第1期', '第2期', '第3期', '第4期', '第5期', '第6期']
            period_amounts = {label: 0 for label in period_labels}
        
        # 按期数排序
        period_labels.sort(key=lambda x: int(x.replace('第', '').replace('期', '')))
        amounts = [period_amounts.get(label, 0) for label in period_labels]
        
        logger.info(f"订单金额分布数据生成完成: {len(period_labels)}个期数")
        
        return {
            'periods': period_labels,
            'amounts': amounts
        }
        
    except Exception as e:
        logger.exception(f"生成订单金额分布数据时出错: {e}")
        return {
            'periods': ['第1期', '第2期', '第3期'],
            'amounts': [0, 0, 0]
        }


def generate_receivable_amount_chart_data(receivable_periods):
    """
    生成待收金额分布图表数据
    生成待收金额的趋势数据
    """
    try:
        periods = []
        amounts = []
        
        for item in receivable_periods:
            if isinstance(item, dict):
                period = item.get('period', 0)
                amount = item.get('amount', 0)
                
                # 清理金额数据
                if isinstance(amount, str):
                    amount = clean_amount_value(amount)
                
                periods.append(f"第{period}期")
                amounts.append(float(amount) if amount else 0)
        
        # 如果没有数据，提供默认数据
        if not periods:
            logger.warning("无待收期数数据，使用默认数据")
            periods = ['第1期', '第2期', '第3期', '第4期', '第5期', '第6期']
            amounts = [0, 0, 0, 0, 0, 0]
        
        # 按期数排序
        combined = list(zip(periods, amounts))
        combined.sort(key=lambda x: int(x[0].replace('第', '').replace('期', '')))
        periods, amounts = zip(*combined) if combined else ([], [])
        
        logger.info(f"待收金额分布数据生成完成: {len(periods)}个期数")
        
        return {
            'periods': list(periods),
            'amounts': list(amounts)
        }
        
    except Exception as e:
        logger.exception(f"生成待收金额分布数据时出错: {e}")
        return {
            'periods': ['第1期', '第2期', '第3期'],
            'amounts': [0, 0, 0]
        }


def generate_business_type_chart_data(order_details, order_summary):
    """
    生成业务类型分布图表数据
    分析订单中的业务类型分布
    """
    try:
        business_types = {}
        
        # 首先尝试从order_summary获取业务类型统计
        if isinstance(order_summary, dict) and 'business_types' in order_summary:
            summary_types = order_summary['business_types']
            if isinstance(summary_types, dict):
                business_types.update(summary_types)
                logger.info("从订单汇总中获取业务类型数据")
        
        # 如果没有汇总数据，从订单详情中统计
        if not business_types and order_details:
            logger.info("从订单详情中统计业务类型分布")
            type_counts = {}
            
            for order in order_details:
                if isinstance(order, dict):
                    # 尝试多个可能的字段名
                    business_type = (
                        order.get('business_type', '') or
                        order.get('product_type', '') or
                        order.get('type', '') or
                        '其他'
                    )
                    
                    # 标准化业务类型名称
                    business_type = normalize_business_type(business_type)
                    
                    if business_type:
                        type_counts[business_type] = type_counts.get(business_type, 0) + 1
            
            # 转换为百分比
            total_orders = sum(type_counts.values())
            if total_orders > 0:
                for btype, count in type_counts.items():
                    business_types[btype] = round((count / total_orders) * 100, 1)
        
        # 确保至少有一些数据
        if not business_types:
            logger.warning("无法提取业务类型数据，使用默认分布")
            business_types = {
                '电商': 60.0,
                '租赁': 35.0,
                '其他': 5.0
            }
        
        logger.info(f"业务类型分布数据生成完成: {business_types}")
        
        return {
            'types': business_types
        }
        
    except Exception as e:
        logger.exception(f"生成业务类型分布数据时出错: {e}")
        return {
            'types': {'电商': 60.0, '租赁': 40.0}
        }


def clean_amount_value(amount_str):
    """
    清理金额字符串，提取数值
    """
    if not amount_str:
        return 0
    
    if isinstance(amount_str, (int, float)):
        return amount_str
    
    # 移除货币符号、逗号、空格
    cleaned = str(amount_str).replace('¥', '').replace('￥', '').replace(',', '').replace(' ', '')
    
    try:
        return float(cleaned)
    except (ValueError, TypeError):
        return 0


def normalize_business_type(business_type):
    """
    标准化业务类型名称
    """
    if not business_type:
        return '其他'
    
    business_type = str(business_type).strip().lower()
    
    # 电商相关
    if any(keyword in business_type for keyword in ['电商', 'ecommerce', '电子商务', '网商', '网购']):
        return '电商'
    
    # 租赁相关
    if any(keyword in business_type for keyword in ['租赁', 'lease', 'rent', '出租', '租借']):
        return '租赁'
    
    # 金融相关
    if any(keyword in business_type for keyword in ['金融', 'finance', '贷款', '信贷']):
        return '金融'
    
    # 其他
    return '其他'


def generate_empty_enterprise_chart_data():
    """
    生成空的企业级图表数据
    """
    return {
        'orderAmounts': [0, 0, 0, 0, 0, 0],
        'receivableAmounts': [0, 0, 0, 0, 0, 0],
        'businessTypes': {'电商': 0, '租赁': 0, '其他': 0},
        'periods': ['第1期', '第2期', '第3期', '第4期', '第5期', '第6期'],
        'metadata': {
            'total_orders': 0,
            'total_periods': 0,
            'customer_name': '',
            'last_updated': datetime.datetime.now().isoformat(),
            'data_source': 'empty_fallback'
        }
    } 