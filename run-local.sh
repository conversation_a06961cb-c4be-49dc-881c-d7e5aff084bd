#!/bin/bash

# 本地运行HDSC查询系统（不使用Docker）
# 适合开发和测试

set -e

GREEN='\033[0;32m'
YELLOW='\033[0;33m'
RED='\033[0;31m'
NC='\033[0m'

log_info() { echo -e "${GREEN}[INFO]${NC} $1"; }
log_warn() { echo -e "${YELLOW}[WARN]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }

show_help() {
    echo "HDSC本地运行工具"
    echo ""
    echo "使用方法: ./run-local.sh [命令]"
    echo ""
    echo "命令:"
    echo "  start     - 启动本地服务"
    echo "  stop      - 停止本地服务"
    echo "  restart   - 重启本地服务"
    echo "  status    - 查看服务状态"
    echo "  install   - 安装依赖"
    echo "  help      - 显示帮助"
    echo ""
    echo "特点:"
    echo "- 代码修改立即生效"
    echo "- 无需Docker"
    echo "- 适合开发调试"
}

check_dependencies() {
    log_info "检查依赖..."
    
    # 检查Python虚拟环境
    if [ ! -d ".venv" ]; then
        log_warn "未找到虚拟环境，正在创建..."
        python3 -m venv .venv
    fi
    
    # 激活虚拟环境
    source .venv/bin/activate
    
    # 检查requirements.txt
    if [ -f "requirements.txt" ]; then
        log_info "安装Python依赖..."
        pip install -r requirements.txt
    else
        log_warn "未找到requirements.txt"
    fi
    
    log_success "依赖检查完成"
}

start_service() {
    log_info "启动本地HDSC服务..."

    # 检查端口是否被占用
    if lsof -i :5000 > /dev/null 2>&1; then
        log_warn "端口5000已被占用"
        echo "当前占用进程:"
        lsof -i :5000
        read -p "是否停止现有进程并继续? (y/N): " choice
        if [[ $choice =~ ^[Yy]$ ]]; then
            pkill -f "python.*run.py" 2>/dev/null || true
            pkill -f "flask.*run" 2>/dev/null || true
            sleep 2
        else
            log_error "启动取消"
            return 1
        fi
    fi

    # 创建必要目录
    mkdir -p logs cache

    # 修复权限问题（如果存在）
    if [ -d "logs" ] && [ ! -w "logs" ]; then
        log_warn "修复logs目录权限..."
        sudo chown -R $USER:$USER logs/ 2>/dev/null || true
    fi

    if [ -d "cache" ] && [ ! -w "cache" ]; then
        log_warn "修复cache目录权限..."
        sudo chown -R $USER:$USER cache/ 2>/dev/null || true
    fi
    
    # 激活虚拟环境
    source .venv/bin/activate
    
    # 设置环境变量
    export FLASK_APP=run.py
    export FLASK_ENV=development
    export PYTHONPATH=$(pwd)
    
    # 启动服务
    log_info "启动Flask应用..."
    echo "访问地址: http://localhost:5000"
    echo "按 Ctrl+C 停止服务"
    echo ""
    
    python run.py
}

stop_service() {
    log_info "停止本地服务..."
    
    # 查找并停止相关进程
    if pgrep -f "python.*run.py" > /dev/null; then
        pkill -f "python.*run.py"
        log_success "已停止Python服务"
    fi
    
    if pgrep -f "flask.*run" > /dev/null; then
        pkill -f "flask.*run"
        log_success "已停止Flask服务"
    fi
    
    log_info "服务已停止"
}

restart_service() {
    log_info "重启本地服务..."
    stop_service
    sleep 2
    start_service
}

check_status() {
    log_info "检查服务状态..."
    
    # 检查进程
    if pgrep -f "python.*run.py" > /dev/null; then
        echo "Python服务: 运行中"
        pgrep -f "python.*run.py" | while read pid; do
            echo "  PID: $pid"
        done
    else
        echo "Python服务: 未运行"
    fi
    
    # 检查端口
    if lsof -i :5000 > /dev/null 2>&1; then
        echo "端口5000: 已占用"
        lsof -i :5000
    else
        echo "端口5000: 空闲"
    fi
    
    # 检查服务可访问性
    if curl -s --connect-timeout 5 http://localhost:5000 > /dev/null 2>&1; then
        log_success "服务可访问: http://localhost:5000"
    else
        log_warn "服务不可访问"
    fi
}

install_deps() {
    log_info "安装依赖..."
    
    # 创建虚拟环境
    if [ ! -d ".venv" ]; then
        log_info "创建Python虚拟环境..."
        python3 -m venv .venv
    fi
    
    # 激活虚拟环境
    source .venv/bin/activate
    
    # 升级pip
    pip install --upgrade pip
    
    # 安装依赖
    if [ -f "requirements.txt" ]; then
        log_info "安装requirements.txt中的依赖..."
        pip install -r requirements.txt
    else
        log_info "安装基础依赖..."
        pip install flask gunicorn requests beautifulsoup4 lxml
    fi
    
    log_success "依赖安装完成"
    log_info "现在可以运行: ./run-local.sh start"
}

case "${1:-help}" in
    "start")
        check_dependencies
        start_service
        ;;
    "stop")
        stop_service
        ;;
    "restart")
        restart_service
        ;;
    "status")
        check_status
        ;;
    "install")
        install_deps
        ;;
    "help"|*)
        show_help
        ;;
esac
