#!/bin/bash

# HDSC查询系统 Docker管理脚本
# 提供简单易用的容器管理界面

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 输出函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_header() {
    echo -e "${CYAN}=== $1 ===${NC}"
}

# 显示菜单
show_menu() {
    clear
    log_header "HDSC查询系统 Docker管理器"
    echo ""
    echo "请选择操作："
    echo "1. 查看容器状态"
    echo "2. 查看实时日志"
    echo "3. 启动服务"
    echo "4. 停止服务"
    echo "5. 重启服务"
    echo "6. 进入容器"
    echo "7. 查看资源使用"
    echo "8. 健康检查"
    echo "9. 清理日志"
    echo "10. 备份数据"
    echo "11. 查看网络信息"
    echo "0. 退出"
    echo ""
    echo -n "请输入选项 (0-11): "
}

# 查看容器状态
check_status() {
    log_header "容器状态"
    echo ""
    
    if sudo docker ps | grep -q "hdsc-query-app"; then
        log_success "容器正在运行"
        echo ""
        sudo docker-compose ps
        echo ""
        
        # 显示详细信息
        container_id=$(sudo docker ps --filter "name=hdsc-query-app" --format "{{.ID}}")
        if [ ! -z "$container_id" ]; then
            echo "容器详细信息："
            sudo docker inspect --format='状态: {{.State.Status}}' $container_id
            sudo docker inspect --format='启动时间: {{.State.StartedAt}}' $container_id
            sudo docker inspect --format='健康状态: {{.State.Health.Status}}' $container_id 2>/dev/null || echo "健康状态: 未配置"
        fi
    else
        log_warn "容器未运行"
        echo ""
        echo "所有容器状态："
        sudo docker-compose ps -a
    fi
    
    echo ""
    read -p "按回车键继续..."
}

# 查看实时日志
view_logs() {
    log_header "实时日志"
    echo ""
    log_info "按 Ctrl+C 退出日志查看"
    echo ""
    sleep 2
    
    sudo docker-compose logs -f hdsc-query-app
}

# 启动服务
start_service() {
    log_header "启动服务"
    echo ""
    
    if sudo docker ps | grep -q "hdsc-query-app"; then
        log_warn "服务已经在运行"
    else
        log_info "正在启动服务..."
        sudo docker-compose up -d
        
        if [ $? -eq 0 ]; then
            log_success "服务启动成功"
            echo ""
            log_info "等待服务就绪..."
            sleep 5
            
            if curl -s --connect-timeout 10 http://localhost:5000 > /dev/null 2>&1; then
                log_success "服务已就绪，可以访问 http://localhost:5000"
            else
                log_warn "服务可能仍在启动中，请稍后再试"
            fi
        else
            log_error "服务启动失败"
        fi
    fi
    
    echo ""
    read -p "按回车键继续..."
}

# 停止服务
stop_service() {
    log_header "停止服务"
    echo ""
    
    if sudo docker ps | grep -q "hdsc-query-app"; then
        log_info "正在停止服务..."
        sudo docker-compose down
        
        if [ $? -eq 0 ]; then
            log_success "服务已停止"
        else
            log_error "服务停止失败"
        fi
    else
        log_warn "服务未运行"
    fi
    
    echo ""
    read -p "按回车键继续..."
}

# 重启服务
restart_service() {
    log_header "重启服务"
    echo ""
    
    log_info "正在重启服务..."
    sudo docker-compose restart
    
    if [ $? -eq 0 ]; then
        log_success "服务重启成功"
        echo ""
        log_info "等待服务就绪..."
        sleep 5
        
        if curl -s --connect-timeout 10 http://localhost:5000 > /dev/null 2>&1; then
            log_success "服务已就绪，可以访问 http://localhost:5000"
        else
            log_warn "服务可能仍在启动中，请稍后再试"
        fi
    else
        log_error "服务重启失败"
    fi
    
    echo ""
    read -p "按回车键继续..."
}

# 进入容器
enter_container() {
    log_header "进入容器"
    echo ""
    
    if sudo docker ps | grep -q "hdsc-query-app"; then
        log_info "正在进入容器..."
        log_info "输入 'exit' 退出容器"
        echo ""
        sudo docker exec -it hdsc-query-app bash
    else
        log_error "容器未运行，无法进入"
        echo ""
        read -p "按回车键继续..."
    fi
}

# 查看资源使用
view_resources() {
    log_header "资源使用情况"
    echo ""
    
    if sudo docker ps | grep -q "hdsc-query-app"; then
        log_info "容器资源使用："
        sudo docker stats --no-stream hdsc-query-app
        echo ""
        
        log_info "系统资源使用："
        echo "内存使用："
        free -h
        echo ""
        echo "磁盘使用："
        df -h | grep -E "(Filesystem|/dev/)"
        echo ""
        
        log_info "Docker镜像大小："
        sudo docker images | grep hdsc-query-app
    else
        log_warn "容器未运行"
    fi
    
    echo ""
    read -p "按回车键继续..."
}

# 健康检查
health_check() {
    log_header "健康检查"
    echo ""
    
    ./check-deployment.sh
    
    echo ""
    read -p "按回车键继续..."
}

# 清理日志
clean_logs() {
    log_header "清理日志"
    echo ""
    
    log_info "当前日志目录大小："
    du -sh logs/ 2>/dev/null || echo "日志目录不存在"
    echo ""
    
    read -p "是否要清理7天前的日志文件？(y/N): " confirm
    if [[ $confirm =~ ^[Yy]$ ]]; then
        log_info "正在清理7天前的日志..."
        find logs/ -name "*.log" -mtime +7 -delete 2>/dev/null || true
        find logs/ -name "*.log.*" -mtime +7 -delete 2>/dev/null || true
        log_success "日志清理完成"
        
        echo ""
        log_info "清理后日志目录大小："
        du -sh logs/ 2>/dev/null || echo "日志目录不存在"
    else
        log_info "取消清理操作"
    fi
    
    echo ""
    read -p "按回车键继续..."
}

# 备份数据
backup_data() {
    log_header "备份数据"
    echo ""
    
    backup_name="hdsc-backup-$(date +%Y%m%d-%H%M%S).tar.gz"
    
    log_info "正在创建备份: $backup_name"
    log_info "备份内容: logs, cache, config.py, docker-compose.yml"
    
    tar -czf "$backup_name" logs cache config.py docker-compose.yml gunicorn_config.py 2>/dev/null || {
        log_warn "部分文件可能不存在，继续备份..."
        tar -czf "$backup_name" --ignore-failed-read logs cache config.py docker-compose.yml gunicorn_config.py 2>/dev/null || true
    }
    
    if [ -f "$backup_name" ]; then
        backup_size=$(du -sh "$backup_name" | cut -f1)
        log_success "备份创建成功: $backup_name (大小: $backup_size)"
        log_info "备份文件位置: $(pwd)/$backup_name"
    else
        log_error "备份创建失败"
    fi
    
    echo ""
    read -p "按回车键继续..."
}

# 查看网络信息
view_network() {
    log_header "网络信息"
    echo ""
    
    log_info "Docker网络："
    sudo docker network ls | grep hdsc || echo "未找到hdsc网络"
    echo ""
    
    log_info "端口监听状态："
    netstat -tlnp 2>/dev/null | grep :5000 || echo "端口5000未监听"
    echo ""
    
    log_info "容器网络配置："
    if sudo docker ps | grep -q "hdsc-query-app"; then
        container_id=$(sudo docker ps --filter "name=hdsc-query-app" --format "{{.ID}}")
        sudo docker inspect --format='IP地址: {{range .NetworkSettings.Networks}}{{.IPAddress}}{{end}}' $container_id
        sudo docker inspect --format='端口映射: {{range $p, $conf := .NetworkSettings.Ports}}{{$p}} -> {{(index $conf 0).HostPort}}{{end}}' $container_id
    else
        echo "容器未运行"
    fi
    
    echo ""
    log_info "测试连接："
    if curl -s --connect-timeout 5 http://localhost:5000 > /dev/null 2>&1; then
        log_success "HTTP连接正常"
        status_code=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:5000)
        echo "HTTP状态码: $status_code"
    else
        log_warn "HTTP连接失败"
    fi
    
    echo ""
    read -p "按回车键继续..."
}

# 主循环
main() {
    while true; do
        show_menu
        read choice
        
        case $choice in
            1) check_status ;;
            2) view_logs ;;
            3) start_service ;;
            4) stop_service ;;
            5) restart_service ;;
            6) enter_container ;;
            7) view_resources ;;
            8) health_check ;;
            9) clean_logs ;;
            10) backup_data ;;
            11) view_network ;;
            0) 
                echo ""
                log_info "感谢使用HDSC Docker管理器！"
                exit 0
                ;;
            *)
                echo ""
                log_error "无效选项，请重新选择"
                sleep 2
                ;;
        esac
    done
}

# 检查必要文件
if [ ! -f "docker-compose.yml" ]; then
    log_error "未找到 docker-compose.yml 文件"
    log_info "请确保在项目根目录运行此脚本"
    exit 1
fi

if [ ! -f "check-deployment.sh" ]; then
    log_warn "未找到 check-deployment.sh 文件，健康检查功能将不可用"
fi

# 启动主程序
main
