#!/bin/bash

# HDSC开发模式切换脚本

set -e

GREEN='\033[0;32m'
YELLOW='\033[0;33m'
RED='\033[0;31m'
NC='\033[0m'

log_info() { echo -e "${GREEN}[INFO]${NC} $1"; }
log_warn() { echo -e "${YELLOW}[WARN]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }

show_help() {
    echo "HDSC开发模式管理"
    echo ""
    echo "使用方法: ./dev-mode.sh [命令]"
    echo ""
    echo "命令:"
    echo "  start     - 启动开发模式（代码实时同步）"
    echo "  stop      - 停止开发模式"
    echo "  restart   - 重启开发模式"
    echo "  prod      - 切换到生产模式"
    echo "  status    - 查看当前模式"
    echo "  help      - 显示帮助"
    echo ""
    echo "开发模式特点:"
    echo "- 代码修改实时生效（无需重新构建）"
    echo "- 启用Flask调试模式"
    echo "- 使用开发服务器"
    echo "- 适合频繁代码修改"
}

start_dev() {
    log_info "启动开发模式..."
    
    # 停止生产模式容器
    sudo docker-compose down 2>/dev/null || true
    
    # 启动开发模式
    if sudo docker-compose -f docker-compose.dev.yml up -d; then
        log_info "开发模式启动成功"
        log_info "代码修改将实时生效，无需重新构建"
        log_warn "注意：这是开发模式，不适合生产环境"
        
        sleep 5
        if curl -s --connect-timeout 10 http://localhost:5000 > /dev/null 2>&1; then
            log_info "服务已就绪: http://localhost:5000"
        else
            log_warn "服务可能仍在启动中"
        fi
    else
        log_error "开发模式启动失败"
        return 1
    fi
}

stop_dev() {
    log_info "停止开发模式..."
    sudo docker-compose -f docker-compose.dev.yml down
    log_info "开发模式已停止"
}

restart_dev() {
    log_info "重启开发模式..."
    sudo docker-compose -f docker-compose.dev.yml restart
    log_info "开发模式重启完成"
}

switch_to_prod() {
    log_info "切换到生产模式..."
    
    # 停止开发模式
    sudo docker-compose -f docker-compose.dev.yml down 2>/dev/null || true
    
    # 启动生产模式
    if ./hdsc start; then
        log_info "已切换到生产模式"
        log_info "代码修改需要重新构建镜像才能生效"
    else
        log_error "切换到生产模式失败"
        return 1
    fi
}

check_status() {
    log_info "检查当前模式..."
    
    # 检查开发模式容器
    if sudo docker ps | grep -q "hdsc-query-app-dev"; then
        echo "当前模式: 开发模式"
        echo "容器名称: hdsc-query-app-dev"
        echo "特点: 代码实时同步，调试模式开启"
        sudo docker ps | grep hdsc-query-app-dev
        return 0
    fi
    
    # 检查生产模式容器
    if sudo docker ps | grep -q "hdsc-query-app"; then
        echo "当前模式: 生产模式"
        echo "容器名称: hdsc-query-app"
        echo "特点: 代码需重新构建，性能优化"
        sudo docker ps | grep hdsc-query-app
        return 0
    fi
    
    echo "当前状态: 未运行"
    echo "可用命令:"
    echo "- 启动开发模式: ./dev-mode.sh start"
    echo "- 启动生产模式: ./hdsc start"
}

case "${1:-help}" in
    "start")
        start_dev
        ;;
    "stop")
        stop_dev
        ;;
    "restart")
        restart_dev
        ;;
    "prod")
        switch_to_prod
        ;;
    "status")
        check_status
        ;;
    "help"|*)
        show_help
        ;;
esac
