#!/bin/bash

# HDSC查询系统Docker部署脚本
# 适用于Ubuntu AMD64平台
# 作者：AI Assistant
# 更新日期：2024年12月

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 输出函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP $1]${NC} $2"
}

# 检查Docker是否安装和可用
check_docker() {
    if ! command -v docker &> /dev/null; then
        log_error "Docker未安装，请先安装Docker"
        echo "安装命令:"
        echo "  sudo apt update"
        echo "  sudo apt install docker.io docker-compose"
        exit 1
    fi

    if ! command -v docker-compose &> /dev/null; then
        log_error "docker-compose未安装，请先安装docker-compose"
        echo "安装命令:"
        echo "  sudo apt install docker-compose"
        exit 1
    fi

    # 检查Docker服务状态
    if ! systemctl is-active --quiet docker 2>/dev/null; then
        log_warn "Docker服务未运行，尝试启动..."
        sudo systemctl start docker
        sudo systemctl enable docker
        sleep 3
    fi

    # 检查Docker连接
    if ! sudo docker version > /dev/null 2>&1; then
        log_error "Docker连接失败，请检查Docker服务状态"
        echo "尝试运行: sudo systemctl status docker"
        exit 1
    fi

    # 检查用户权限
    if ! groups $USER | grep -q docker; then
        log_warn "用户不在docker组中，添加用户权限..."
        sudo usermod -aG docker $USER
        log_warn "需要重新登录或运行 'newgrp docker' 使权限生效"
        log_warn "当前会话将使用sudo运行docker命令"
    fi

    log_info "Docker版本: $(sudo docker --version)"
    log_info "Docker Compose版本: $(docker-compose --version)"
}

# 检查项目文件
check_project_files() {
    local required_files=("Dockerfile" "docker-compose.yml" "requirements.txt" "run.py" "app")
    
    for file in "${required_files[@]}"; do
        if [ ! -e "$file" ]; then
            log_error "缺少必要文件或目录: $file"
            exit 1
        fi
    done
    
    log_info "项目文件检查通过"
}

# 创建必要目录
create_directories() {
    log_info "创建必要目录..."
    mkdir -p logs cache
    chmod 755 logs cache
    log_info "目录创建完成"
}

# 停止并删除现有容器
cleanup_existing() {
    log_info "清理现有容器..."

    # 使用sudo确保权限
    if sudo docker ps -a | grep -q "hdsc-query-app"; then
        log_info "停止现有容器..."
        sudo docker-compose down || true

        # 删除现有镜像（可选）
        if [ "$1" = "--rebuild" ]; then
            log_info "删除现有镜像..."
            sudo docker rmi hdsc-query-app:latest || true
        fi
    fi

    # 修复目录权限
    log_info "修复目录权限..."
    sudo chown -R $USER:$USER logs/ cache/ 2>/dev/null || true

    log_info "清理完成"
}

# 构建Docker镜像
build_image() {
    log_info "构建Docker镜像..."

    # 使用sudo确保权限
    sudo docker-compose build --no-cache

    if [ $? -eq 0 ]; then
        log_info "镜像构建成功"

        # 显示镜像信息
        echo ""
        log_info "镜像信息:"
        sudo docker images | grep hdsc-query-app || echo "未找到镜像"
    else
        log_error "镜像构建失败"
        echo "请检查Dockerfile和依赖文件"
        exit 1
    fi
}

# 启动服务
start_services() {
    log_info "启动服务..."

    # 使用sudo确保权限
    sudo docker-compose up -d

    if [ $? -eq 0 ]; then
        log_info "服务启动成功"

        # 显示容器状态
        echo ""
        log_info "容器状态:"
        sudo docker-compose ps
    else
        log_error "服务启动失败"
        echo "查看错误日志:"
        sudo docker-compose logs
        exit 1
    fi
}

# 等待服务就绪
wait_for_service() {
    log_info "等待服务就绪..."
    
    local max_attempts=30
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if curl -s --connect-timeout 5 http://localhost:5000 > /dev/null 2>&1; then
            log_info "服务已就绪"
            return 0
        fi
        
        log_info "等待服务启动... ($attempt/$max_attempts)"
        sleep 5
        ((attempt++))
    done
    
    log_error "服务启动超时"
    return 1
}

# 健康检查
health_check() {
    log_info "执行健康检查..."
    
    # 检查容器状态
    local container_status=$(docker inspect --format='{{.State.Status}}' hdsc-query-app 2>/dev/null || echo "not_found")
    log_info "容器状态: $container_status"
    
    # 检查端口监听
    if docker exec hdsc-query-app netstat -tlnp 2>/dev/null | grep -q ":5000" || \
       docker exec hdsc-query-app ss -tlnp 2>/dev/null | grep -q ":5000"; then
        log_info "✓ 端口5000正在监听"
    else
        log_warn "✗ 端口5000未在监听"
    fi
    
    # 检查HTTP响应
    if curl -s --connect-timeout 10 http://localhost:5000 > /dev/null 2>&1; then
        log_info "✓ HTTP连接测试成功"
    else
        log_warn "✗ HTTP连接测试失败"
    fi
    
    # 显示容器日志（最后20行）
    log_info "容器日志（最后20行）:"
    docker-compose logs --tail=20 hdsc-query-app
}

# 显示部署信息
show_deployment_info() {
    log_info "部署完成！"
    echo ""
    echo "=== 部署信息 ==="
    echo "服务地址: http://localhost:5000"
    echo "容器名称: hdsc-query-app"
    echo "网络名称: hdsc-network"
    echo ""
    echo "=== 管理命令 ==="
    echo "查看服务状态: docker-compose ps"
    echo "查看日志: docker-compose logs -f hdsc-query-app"
    echo "停止服务: docker-compose down"
    echo "重启服务: docker-compose restart"
    echo "进入容器: docker exec -it hdsc-query-app bash"
    echo ""
    echo "=== 目录挂载 ==="
    echo "日志目录: ./logs -> /app/logs"
    echo "缓存目录: ./cache -> /app/cache"
    echo "文档目录: ./AHT, ./BHT, ./HZ -> /app/AHT, /app/BHT, /app/HZ"
}

# 主函数
main() {
    log_step 1 "检查环境"
    check_docker
    check_project_files
    
    log_step 2 "准备部署"
    create_directories
    cleanup_existing "$1"
    
    log_step 3 "构建镜像"
    build_image
    
    log_step 4 "启动服务"
    start_services
    
    log_step 5 "等待服务就绪"
    if wait_for_service; then
        log_step 6 "健康检查"
        health_check
        
        log_step 7 "部署完成"
        show_deployment_info
    else
        log_error "部署失败，请检查日志"
        docker-compose logs hdsc-query-app
        exit 1
    fi
}

# 查看服务状态
show_status() {
    log_info "查看服务状态..."

    echo "=== Docker服务状态 ==="
    if systemctl is-active --quiet docker; then
        echo "Docker服务: ✅ 运行中"
    else
        echo "Docker服务: ❌ 未运行"
    fi

    echo ""
    echo "=== 容器状态 ==="
    sudo docker-compose ps 2>/dev/null || echo "无容器运行"

    echo ""
    echo "=== 镜像信息 ==="
    sudo docker images | grep hdsc-query-app || echo "未找到HDSC镜像"

    echo ""
    echo "=== 端口占用 ==="
    if lsof -i :5000 2>/dev/null; then
        echo ""
    else
        echo "端口5000: 空闲"
    fi

    echo ""
    echo "=== 服务可访问性 ==="
    if curl -s --connect-timeout 5 http://localhost:5000 > /dev/null 2>&1; then
        echo "服务状态: ✅ 可访问 (http://localhost:5000)"
    else
        echo "服务状态: ❌ 不可访问"
    fi
}

# 查看日志
show_logs() {
    log_info "查看容器日志..."

    if sudo docker-compose ps | grep -q hdsc-query-app; then
        echo "实时日志 (按 Ctrl+C 退出):"
        sudo docker-compose logs -f hdsc-query-app
    else
        log_warn "容器未运行"
        echo "查看最近的日志:"
        sudo docker-compose logs hdsc-query-app 2>/dev/null || echo "无日志可显示"
    fi
}

# 停止服务
stop_services() {
    log_info "停止服务..."

    if sudo docker-compose down; then
        log_info "服务已停止"
    else
        log_warn "停止服务时遇到问题"
    fi
}

# 重启服务
restart_services() {
    log_info "重启服务..."
    stop_services
    sleep 2
    start_services
    wait_for_service
}

# 显示帮助信息
show_help() {
    echo "HDSC查询系统Docker部署脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  --rebuild    重新构建镜像（删除现有镜像）"
    echo "  --status     查看服务状态"
    echo "  --logs       查看容器日志"
    echo "  --stop       停止服务"
    echo "  --restart    重启服务"
    echo "  --help       显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0           # 正常部署"
    echo "  $0 --rebuild # 重新构建并部署"
    echo "  $0 --status  # 查看状态"
    echo "  $0 --logs    # 查看日志"
}

# 解析命令行参数
case "${1:-}" in
    --help)
        show_help
        exit 0
        ;;
    --rebuild)
        main --rebuild
        ;;
    --status)
        show_status
        exit 0
        ;;
    --logs)
        show_logs
        exit 0
        ;;
    --stop)
        stop_services
        exit 0
        ;;
    --restart)
        restart_services
        exit 0
        ;;
    "")
        main
        ;;
    *)
        log_error "未知选项: $1"
        show_help
        exit 1
        ;;
esac
