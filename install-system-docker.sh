#!/bin/bash

# 安装系统Docker替代snap Docker
# 解决snap Docker连接问题

set -e

GREEN='\033[0;32m'
YELLOW='\033[0;33m'
RED='\033[0;31m'
NC='\033[0m'

log_info() { echo -e "${GREEN}[INFO]${NC} $1"; }
log_warn() { echo -e "${YELLOW}[WARN]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }

show_help() {
    echo "Docker安装脚本"
    echo ""
    echo "使用方法: ./install-system-docker.sh [选项]"
    echo ""
    echo "选项:"
    echo "  --install     安装系统Docker（推荐）"
    echo "  --check       检查当前Docker状态"
    echo "  --fix-snap    尝试修复snap Docker"
    echo "  --help        显示帮助"
    echo ""
    echo "说明:"
    echo "- 系统Docker通常比snap Docker更稳定"
    echo "- 安装过程会保留现有的镜像和容器"
    echo "- 建议在snap Docker有问题时使用"
}

check_docker_status() {
    log_info "检查Docker状态..."
    
    echo "=== Snap Docker ==="
    if command -v snap > /dev/null 2>&1; then
        sudo snap services docker 2>/dev/null || echo "Snap Docker未安装"
        if sudo docker version > /dev/null 2>&1; then
            echo "Snap Docker: 可用"
        else
            echo "Snap Docker: 不可用"
        fi
    else
        echo "Snap未安装"
    fi
    
    echo ""
    echo "=== 系统Docker ==="
    if systemctl is-active --quiet docker 2>/dev/null; then
        echo "系统Docker服务: 运行中"
        if docker version > /dev/null 2>&1; then
            echo "系统Docker: 可用"
        else
            echo "系统Docker: 服务运行但连接失败"
        fi
    else
        echo "系统Docker服务: 未运行或未安装"
    fi
    
    echo ""
    echo "=== 推荐操作 ==="
    if sudo docker version > /dev/null 2>&1; then
        log_success "当前Docker可用，无需操作"
    else
        log_warn "Docker不可用，建议安装系统Docker"
        echo "运行: ./install-system-docker.sh --install"
    fi
}

install_system_docker() {
    log_info "开始安装系统Docker..."
    
    # 检查是否已安装
    if systemctl is-active --quiet docker 2>/dev/null && docker version > /dev/null 2>&1; then
        log_success "系统Docker已安装并运行"
        return 0
    fi
    
    # 更新包列表
    log_info "更新包列表..."
    sudo apt update
    
    # 安装必要的包
    log_info "安装依赖包..."
    sudo apt install -y apt-transport-https ca-certificates curl gnupg lsb-release
    
    # 添加Docker官方GPG密钥
    log_info "添加Docker GPG密钥..."
    curl -fsSL https://download.docker.com/linux/ubuntu/gpg | sudo gpg --dearmor -o /usr/share/keyrings/docker-archive-keyring.gpg
    
    # 添加Docker仓库
    log_info "添加Docker仓库..."
    echo "deb [arch=$(dpkg --print-architecture) signed-by=/usr/share/keyrings/docker-archive-keyring.gpg] https://download.docker.com/linux/ubuntu $(lsb_release -cs) stable" | sudo tee /etc/apt/sources.list.d/docker.list > /dev/null
    
    # 更新包列表
    sudo apt update
    
    # 安装Docker
    log_info "安装Docker Engine..."
    sudo apt install -y docker-ce docker-ce-cli containerd.io docker-compose-plugin
    
    # 启动Docker服务
    log_info "启动Docker服务..."
    sudo systemctl start docker
    sudo systemctl enable docker
    
    # 添加用户到docker组
    log_info "添加用户到docker组..."
    sudo usermod -aG docker $USER
    
    # 验证安装
    log_info "验证Docker安装..."
    if sudo docker version > /dev/null 2>&1; then
        log_success "系统Docker安装成功！"
        
        # 显示版本信息
        echo ""
        sudo docker version
        
        echo ""
        log_info "重要提示:"
        echo "1. 请重新登录或运行 'newgrp docker' 以使用户组生效"
        echo "2. 现在可以不使用sudo运行docker命令"
        echo "3. 如果有snap Docker，建议停用: sudo snap disable docker"
        
        return 0
    else
        log_error "Docker安装失败"
        return 1
    fi
}

fix_snap_docker() {
    log_info "尝试修复snap Docker..."
    
    # 完全重置snap Docker
    log_info "停止snap Docker..."
    sudo snap stop docker 2>/dev/null || true
    
    log_info "清理Docker进程..."
    sudo pkill -f dockerd 2>/dev/null || true
    sudo pkill -f containerd 2>/dev/null || true
    sleep 5
    
    log_info "清理Docker数据..."
    sudo rm -rf /var/snap/docker/common/var-lib-docker/containers/* 2>/dev/null || true
    sudo rm -rf /var/snap/docker/common/var-lib-docker/overlay2/* 2>/dev/null || true
    
    log_info "重新启动snap Docker..."
    sudo snap start docker
    
    # 等待启动
    sleep 20
    
    if sudo docker version > /dev/null 2>&1; then
        log_success "Snap Docker修复成功！"
        return 0
    else
        log_error "Snap Docker修复失败"
        log_warn "建议安装系统Docker: ./install-system-docker.sh --install"
        return 1
    fi
}

case "${1:-help}" in
    "--install")
        install_system_docker
        ;;
    "--check")
        check_docker_status
        ;;
    "--fix-snap")
        fix_snap_docker
        ;;
    "--help"|*)
        show_help
        ;;
esac
