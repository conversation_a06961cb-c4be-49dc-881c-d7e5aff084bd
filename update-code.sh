#!/bin/bash

# HDSC查询系统代码更新脚本
# 用于同步最新代码到Docker容器

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() { echo -e "${GREEN}[INFO]${NC} $1"; }
log_warn() { echo -e "${YELLOW}[WARN]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }

show_help() {
    echo "HDSC代码更新工具"
    echo ""
    echo "使用方法: ./update-code.sh [选项]"
    echo ""
    echo "选项:"
    echo "  --rebuild     重新构建镜像并部署（推荐）"
    echo "  --restart     仅重启容器（适用于配置文件更改）"
    echo "  --quick       快速重启（不重新构建）"
    echo "  --check       检查当前状态"
    echo "  --help        显示帮助"
    echo ""
    echo "推荐流程:"
    echo "  1. 代码更改后: ./update-code.sh --rebuild"
    echo "  2. 配置更改后: ./update-code.sh --restart"
    echo "  3. 快速测试: ./update-code.sh --quick"
}

check_status() {
    log_info "检查当前状态..."
    
    # 检查Docker状态
    if ! sudo docker version > /dev/null 2>&1; then
        log_error "Docker服务未运行"
        return 1
    fi
    
    # 检查容器状态
    if sudo docker ps | grep -q "hdsc-query-app"; then
        log_info "容器正在运行"
        sudo docker ps | grep hdsc-query-app
    else
        log_warn "容器未运行"
        sudo docker ps -a | grep hdsc-query-app || echo "未找到hdsc容器"
    fi
    
    # 检查镜像
    echo ""
    log_info "当前镜像信息:"
    sudo docker images | grep hdsc-query-app || echo "未找到hdsc镜像"
    
    return 0
}

rebuild_and_deploy() {
    log_info "开始重新构建和部署..."
    
    # 1. 停止现有容器
    log_info "停止现有容器..."
    if ! ./hdsc stop; then
        log_warn "停止容器时遇到问题，继续执行..."
    fi
    
    # 2. 删除旧镜像（可选，节省空间）
    log_info "清理旧镜像..."
    sudo docker rmi hdsc-query-app:latest 2>/dev/null || log_warn "未找到旧镜像或删除失败"
    
    # 3. 重新构建镜像
    log_info "重新构建Docker镜像..."
    if sudo docker-compose build --no-cache; then
        log_success "镜像构建成功"
    else
        log_error "镜像构建失败"
        return 1
    fi
    
    # 4. 启动新容器
    log_info "启动新容器..."
    if ./hdsc start; then
        log_success "容器启动成功"
    else
        log_error "容器启动失败"
        return 1
    fi
    
    # 5. 等待服务就绪
    log_info "等待服务就绪..."
    sleep 10
    
    # 6. 验证服务
    if curl -s --connect-timeout 10 http://localhost:5000 > /dev/null 2>&1; then
        log_success "服务验证成功！"
        log_info "访问地址: http://localhost:5000"
    else
        log_warn "服务可能仍在启动中，请稍后检查"
    fi
    
    return 0
}

restart_container() {
    log_info "重启容器（保持现有镜像）..."
    
    # 停止容器
    if ! ./hdsc stop; then
        log_warn "停止容器时遇到问题，继续执行..."
    fi
    
    # 启动容器
    if ./hdsc start; then
        log_success "容器重启成功"
        
        # 等待服务就绪
        log_info "等待服务就绪..."
        sleep 5
        
        if curl -s --connect-timeout 10 http://localhost:5000 > /dev/null 2>&1; then
            log_success "服务验证成功！"
        else
            log_warn "服务可能仍在启动中"
        fi
    else
        log_error "容器重启失败"
        return 1
    fi
    
    return 0
}

quick_restart() {
    log_info "快速重启容器..."
    
    if ./hdsc restart; then
        log_success "快速重启成功"
        
        # 简单验证
        sleep 3
        if curl -s --connect-timeout 5 http://localhost:5000 > /dev/null 2>&1; then
            log_success "服务正常运行"
        else
            log_warn "服务可能仍在启动中"
        fi
    else
        log_error "快速重启失败"
        return 1
    fi
    
    return 0
}

# 主程序
case "${1:-help}" in
    "--rebuild")
        log_info "执行完整重建部署..."
        rebuild_and_deploy
        ;;
    
    "--restart")
        log_info "执行容器重启..."
        restart_container
        ;;
    
    "--quick")
        log_info "执行快速重启..."
        quick_restart
        ;;
    
    "--check")
        check_status
        ;;
    
    "--help"|"help"|*)
        show_help
        ;;
esac

echo ""
log_info "操作完成！"
echo ""
echo "后续操作:"
echo "- 查看状态: ./hdsc status"
echo "- 查看日志: ./hdsc logs"
echo "- 健康检查: ./hdsc check"
