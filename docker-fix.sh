#!/bin/bash

# Docker容器问题修复脚本
# 专门处理容器无法停止的权限问题

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() { echo -e "${GREEN}[INFO]${NC} $1"; }
log_warn() { echo -e "${YELLOW}[WARN]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }

show_help() {
    echo "Docker容器问题修复工具"
    echo ""
    echo "使用方法: ./docker-fix.sh [选项]"
    echo ""
    echo "选项:"
    echo "  --force-stop    强制停止所有容器"
    echo "  --restart-docker 重启Docker服务"
    echo "  --clean-restart  完全清理并重启"
    echo "  --check         检查Docker状态"
    echo "  --help          显示帮助"
    echo ""
    echo "常见问题解决:"
    echo "  1. 容器无法停止: ./docker-fix.sh --force-stop"
    echo "  2. 权限问题: ./docker-fix.sh --restart-docker"
    echo "  3. 完全重置: ./docker-fix.sh --clean-restart"
}

check_docker_status() {
    log_info "检查Docker状态..."
    
    # 检查Docker服务
    if sudo docker version > /dev/null 2>&1; then
        log_success "Docker服务正常"
    else
        log_error "Docker服务异常"
        return 1
    fi
    
    # 检查容器状态
    echo ""
    log_info "容器状态:"
    sudo docker ps -a | grep hdsc || echo "未找到hdsc容器"
    
    # 检查网络
    echo ""
    log_info "Docker网络:"
    sudo docker network ls | grep hdsc || echo "未找到hdsc网络"
    
    return 0
}

force_stop_containers() {
    log_info "强制停止所有相关容器..."

    # 尝试正常停止
    if sudo docker-compose down --timeout 5 2>/dev/null; then
        log_success "容器正常停止"
        return 0
    fi

    log_warn "正常停止失败，尝试强制停止..."

    # 方法1: 使用docker-compose强制停止
    log_info "尝试docker-compose强制停止..."
    sudo docker-compose kill 2>/dev/null || true
    sudo docker-compose rm -f 2>/dev/null || true

    # 方法2: 直接操作容器
    container_ids=$(sudo docker ps -aq --filter "name=hdsc" 2>/dev/null || true)

    if [ ! -z "$container_ids" ]; then
        log_info "找到容器: $container_ids"

        # 强制停止容器
        for id in $container_ids; do
            log_info "强制停止容器: $id"

            # 尝试多种停止方法
            sudo docker stop "$id" --time=1 2>/dev/null || true
            sleep 1
            sudo docker kill "$id" 2>/dev/null || true
            sleep 1
            sudo docker rm -f "$id" 2>/dev/null || true
        done

        # 验证是否真的停止了
        remaining=$(sudo docker ps -q --filter "name=hdsc" 2>/dev/null || true)
        if [ -z "$remaining" ]; then
            log_success "容器强制停止完成"
        else
            log_warn "仍有容器在运行，尝试重启Docker服务..."
            restart_docker_service

            # 重启后再次尝试清理
            remaining=$(sudo docker ps -aq --filter "name=hdsc" 2>/dev/null || true)
            if [ ! -z "$remaining" ]; then
                for id in $remaining; do
                    sudo docker rm -f "$id" 2>/dev/null || true
                done
            fi
        fi
    else
        log_info "未找到运行中的容器"
    fi

    # 清理网络
    sudo docker network rm hdsc-network 2>/dev/null || true

    return 0
}

restart_docker_service() {
    log_info "重启Docker服务..."
    
    # 停止Docker
    log_info "停止Docker服务..."
    sudo snap stop docker
    
    # 等待停止完成
    sleep 3
    
    # 启动Docker
    log_info "启动Docker服务..."
    sudo snap start docker
    
    # 等待启动完成
    sleep 5
    
    # 验证Docker状态
    if sudo docker version > /dev/null 2>&1; then
        log_success "Docker服务重启成功"
        return 0
    else
        log_error "Docker服务重启失败"
        return 1
    fi
}

clean_restart() {
    log_info "执行完全清理并重启..."
    
    # 1. 强制停止容器
    force_stop_containers
    
    # 2. 重启Docker服务
    restart_docker_service
    
    # 3. 清理Docker资源
    log_info "清理Docker资源..."
    sudo docker system prune -f 2>/dev/null || true
    
    # 4. 重新启动应用
    log_info "重新启动HDSC应用..."
    if sudo docker-compose up -d; then
        log_success "应用重启成功"
        
        # 等待应用就绪
        log_info "等待应用就绪..."
        sleep 10
        
        # 检查应用状态
        if curl -s --connect-timeout 10 http://localhost:5000 > /dev/null 2>&1; then
            log_success "应用已就绪，访问地址: http://localhost:5000"
        else
            log_warn "应用可能仍在启动中"
        fi
    else
        log_error "应用重启失败"
        return 1
    fi
    
    return 0
}

# 主程序
case "${1:-help}" in
    "--force-stop")
        force_stop_containers
        ;;
    
    "--restart-docker")
        restart_docker_service
        ;;
    
    "--clean-restart")
        clean_restart
        ;;
    
    "--check")
        check_docker_status
        ;;
    
    "--help"|"help"|*)
        show_help
        ;;
esac
