services:
  hdsc-query-app:
    build:
      context: .
      dockerfile: Dockerfile
    image: hdsc-query-app:latest
    container_name: hdsc-query-app-dev
    restart: unless-stopped
    ports:
      - "5000:5000"
    volumes:
      # 开发模式：挂载整个应用代码目录
      - .:/app
      # 排除不需要挂载的目录
      - /app/__pycache__
      - /app/.git
      - /app/logs
      - /app/cache
      # 重新挂载日志和缓存到主机
      - ./logs:/app/logs
      - ./cache:/app/cache
    environment:
      - FLASK_ENV=development
      - FLASK_APP=run.py
      - PYTHONPATH=/app
      - FLASK_DEBUG=1
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - hdsc-network
    # 开发模式：使用开发服务器而不是gunicorn
    command: python run.py

networks:
  hdsc-network:
    driver: bridge
    name: hdsc-network
