/**
 * 搜索功能代码片段
 * 需要添加到 DesktopDataGridManager 类中
 */

// 1. 在构造函数中添加搜索状态管理（已完成）

// 2. 在 init() 方法中添加搜索系统初始化调用（已完成）

// 3. 添加以下搜索相关方法：

/**
 * 初始化搜索系统
 */
initSearchSystem() {
    // 加载搜索历史
    this.searchState.searchHistory = this.loadSearchHistory();

    // 为每个标签页初始化搜索功能
    const searchInputs = document.querySelectorAll('.search-input');
    searchInputs.forEach(input => {
        this.initSearchInput(input);
    });

    // 初始化快捷键支持
    this.initSearchShortcuts();

    // 初始化统计信息
    this.updateSearchStats();

    console.log('搜索系统初始化完成，支持实时搜索和快捷键');
}

/**
 * 初始化搜索输入框
 */
initSearchInput(input) {
    const tabType = input.getAttribute('data-tab');
    const wrapper = input.closest('.search-input-wrapper');
    const clearBtn = wrapper.querySelector('.clear-search-btn');

    // 实时搜索监听
    let searchTimeout;
    input.addEventListener('input', (e) => {
        clearTimeout(searchTimeout);
        searchTimeout = setTimeout(() => {
            this.performSearch(e.target.value.trim(), tabType);
        }, 300); // 防抖延迟300ms
    });

    // 清除搜索按钮
    if (clearBtn) {
        clearBtn.addEventListener('click', () => {
            input.value = '';
            this.clearSearch(tabType);
            input.focus();
        });
    }

    // 回车键触发搜索
    input.addEventListener('keydown', (e) => {
        if (e.key === 'Enter') {
            e.preventDefault();
            this.performSearch(input.value.trim(), tabType);
            this.saveToSearchHistory(input.value.trim());
        }
    });

    // 焦点事件
    input.addEventListener('focus', () => {
        this.searchState.isSearchActive = true;
    });

    input.addEventListener('blur', () => {
        setTimeout(() => {
            this.searchState.isSearchActive = false;
        }, 200);
    });

    console.log(`搜索输入框初始化完成: ${tabType}`);
}

/**
 * 执行搜索操作
 */
performSearch(query, tabType) {
    this.searchState.currentQuery = query;

    if (!query) {
        this.clearSearch(tabType);
        return;
    }

    const container = document.querySelector(`#${tabType} .data-grid-container`);
    if (!container) return;

    const cards = container.querySelectorAll('.desktop-data-card');
    let visibleCount = 0;
    let hasResults = false;

    // 清除之前的高亮和状态
    this.clearHighlights(container);

    cards.forEach(card => {
        const isMatch = this.isCardMatch(card, query, tabType);
        
        if (isMatch) {
            this.showCard(card);
            this.highlightMatches(card, query);
            card.classList.add('search-matched');
            visibleCount++;
            hasResults = true;
        } else {
            this.hideCard(card);
        }
    });

    // 更新搜索统计
    this.searchState.searchResults[tabType] = {
        visible: visibleCount,
        total: cards.length
    };

    this.updateSearchStats(tabType);

    // 显示空状态或结果
    this.toggleEmptySearchState(container, !hasResults, query);

    console.log(`搜索完成: "${query}" 在 ${tabType} 中找到 ${visibleCount} 条结果`);
}

/**
 * 判断卡片是否匹配搜索条件
 */
isCardMatch(card, query, tabType) {
    const searchableText = this.getSearchableText(card, tabType);
    const normalizedQuery = query.toLowerCase();
    
    // 支持多关键词搜索（用空格分隔）
    const keywords = normalizedQuery.split(/\s+/).filter(k => k.length > 0);
    
    return keywords.every(keyword => 
        searchableText.includes(keyword)
    );
}

/**
 * 获取卡片的可搜索文本
 */
getSearchableText(card, tabType) {
    let searchableText = '';

    switch (tabType) {
        case 'receivable':
            // 待收明细：期数、金额、状态、订单数量、设备台数
            searchableText = [
                card.querySelector('.period-number')?.textContent || '',
                card.querySelector('.data-value.amount')?.textContent || '',
                card.querySelector('.status-badge')?.textContent || '',
                ...Array.from(card.querySelectorAll('.data-value')).map(el => el.textContent || '')
            ].join(' ').toLowerCase();
            break;

        case 'orders':
            // 订单详情：订单号、日期、业务类型、产品类型、金额等
            searchableText = [
                card.querySelector('.order-number')?.textContent || '',
                card.querySelector('.order-date')?.textContent || '',
                card.querySelector('.type-badge')?.textContent || '',
                ...Array.from(card.querySelectorAll('.detail-value')).map(el => el.textContent || ''),
                ...Array.from(card.querySelectorAll('.amount-value')).map(el => el.textContent || '')
            ].join(' ').toLowerCase();
            break;

        case 'finance':
            // 财务流水：流水号、交易类型、金额、方向、订单号等
            searchableText = [
                card.querySelector('.transaction-type')?.textContent || '',
                card.querySelector('.flow-badge')?.textContent || '',
                card.querySelector('.amount-value')?.textContent || '',
                ...Array.from(card.querySelectorAll('.detail-value')).map(el => el.textContent || '')
            ].join(' ').toLowerCase();
            break;

        default:
            searchableText = card.textContent.toLowerCase();
    }

    return searchableText;
}

/**
 * 高亮匹配的文本
 */
highlightMatches(card, query) {
    const keywords = query.toLowerCase().split(/\s+/).filter(k => k.length > 0);
    const textNodes = this.getTextNodes(card);

    textNodes.forEach(node => {
        const text = node.textContent.toLowerCase();
        let hasMatch = false;
        let highlightedText = node.textContent;

        keywords.forEach(keyword => {
            if (text.includes(keyword)) {
                const regex = new RegExp(`(${this.escapeRegex(keyword)})`, 'gi');
                highlightedText = highlightedText.replace(regex, '<span class="search-highlight">$1</span>');
                hasMatch = true;
            }
        });

        if (hasMatch) {
            const wrapper = document.createElement('span');
            wrapper.innerHTML = highlightedText;
            node.parentNode.replaceChild(wrapper, node);
        }
    });
}

/**
 * 获取所有文本节点
 */
getTextNodes(element) {
    const textNodes = [];
    const walker = document.createTreeWalker(
        element,
        NodeFilter.SHOW_TEXT,
        {
            acceptNode: function(node) {
                if (node.parentElement.tagName === 'SCRIPT' || 
                    node.parentElement.tagName === 'STYLE') {
                    return NodeFilter.FILTER_REJECT;
                }
                return node.textContent.trim() ? 
                    NodeFilter.FILTER_ACCEPT : 
                    NodeFilter.FILTER_REJECT;
            }
        }
    );

    let node;
    while (node = walker.nextNode()) {
        textNodes.push(node);
    }

    return textNodes;
}

/**
 * 转义正则表达式特殊字符
 */
escapeRegex(string) {
    return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
}

/**
 * 显示卡片
 */
showCard(card) {
    card.classList.remove('search-hidden');
    card.style.display = '';
}

/**
 * 隐藏卡片
 */
hideCard(card) {
    card.classList.add('search-hidden');
}

/**
 * 清除搜索
 */
clearSearch(tabType) {
    const container = document.querySelector(`#${tabType} .data-grid-container`);
    if (!container) return;

    const cards = container.querySelectorAll('.desktop-data-card');
    
    cards.forEach(card => {
        this.showCard(card);
        card.classList.remove('search-matched');
    });

    this.clearHighlights(container);
    this.hideEmptySearchState(container);

    // 重置搜索统计
    const totalCount = cards.length;
    this.searchState.searchResults[tabType] = {
        visible: totalCount,
        total: totalCount
    };

    this.updateSearchStats(tabType);
    this.searchState.currentQuery = '';

    console.log(`清除搜索: ${tabType}`);
}

/**
 * 清除高亮显示
 */
clearHighlights(container) {
    const highlights = container.querySelectorAll('.search-highlight');
    highlights.forEach(highlight => {
        const parent = highlight.parentNode;
        parent.replaceChild(document.createTextNode(highlight.textContent), highlight);
        parent.normalize();
    });
}

/**
 * 更新搜索统计信息
 */
updateSearchStats(tabType = null) {
    const tabs = tabType ? [tabType] : ['receivable', 'orders', 'finance'];

    tabs.forEach(tab => {
        const container = document.querySelector(`#${tab} .search-stats`);
        if (!container) return;

        // 计算实际数据数量
        const dataContainer = document.querySelector(`#${tab} .data-grid-container`);
        const totalCards = dataContainer ? dataContainer.querySelectorAll('.desktop-data-card').length : 0;
        const visibleCards = dataContainer ? dataContainer.querySelectorAll('.desktop-data-card:not(.search-hidden)').length : 0;

        const visibleCount = container.querySelector('.visible-count');
        const totalCount = container.querySelector('.total-count');

        if (visibleCount && totalCount) {
            visibleCount.textContent = visibleCards;
            totalCount.textContent = totalCards;
        }
    });
}

/**
 * 切换空搜索状态
 */
toggleEmptySearchState(container, show, query) {
    let emptyState = container.querySelector('.search-empty-state');

    if (show) {
        if (!emptyState) {
            emptyState = document.createElement('div');
            emptyState.className = 'search-empty-state';
            emptyState.innerHTML = `
                <i class="bi bi-search"></i>
                <h5>未找到匹配结果</h5>
                <p>没有找到包含 "${query}" 的数据项</p>
            `;
            container.appendChild(emptyState);
        }
    } else {
        if (emptyState) {
            emptyState.remove();
        }
    }
}

/**
 * 隐藏空搜索状态
 */
hideEmptySearchState(container) {
    const emptyState = container.querySelector('.search-empty-state');
    if (emptyState) {
        emptyState.remove();
    }
}

/**
 * 初始化搜索快捷键
 */
initSearchShortcuts() {
    document.addEventListener('keydown', (e) => {
        // Ctrl+F 激活搜索
        if (e.ctrlKey && e.key === 'f') {
            e.preventDefault();
            this.focusCurrentTabSearch();
        }
        
        // Escape 清除搜索
        if (e.key === 'Escape' && this.searchState.isSearchActive) {
            this.clearCurrentTabSearch();
        }
    });

    console.log('搜索快捷键初始化完成：Ctrl+F 激活搜索，Escape 清除搜索');
}

/**
 * 聚焦当前标签页的搜索框
 */
focusCurrentTabSearch() {
    const currentTab = this.currentActiveTab;
    const searchInput = document.querySelector(`#${currentTab} .search-input`);
    
    if (searchInput) {
        searchInput.focus();
        searchInput.select();
        console.log(`激活搜索框: ${currentTab}`);
    }
}

/**
 * 清除当前标签页的搜索
 */
clearCurrentTabSearch() {
    const currentTab = this.currentActiveTab;
    const searchInput = document.querySelector(`#${currentTab} .search-input`);
    
    if (searchInput && searchInput.value) {
        searchInput.value = '';
        this.clearSearch(currentTab);
        console.log(`清除搜索: ${currentTab}`);
    }
}

/**
 * 保存搜索历史
 */
saveToSearchHistory(query) {
    if (!query || query.length < 2) return;

    const history = this.searchState.searchHistory;
    const index = history.indexOf(query);
    
    if (index > -1) {
        history.splice(index, 1);
    }
    
    history.unshift(query);
    
    // 限制历史记录数量
    if (history.length > 10) {
        history.splice(10);
    }
    
    this.searchState.searchHistory = history;
    this.saveSearchHistory();
}

/**
 * 加载搜索历史
 */
loadSearchHistory() {
    try {
        const history = localStorage.getItem('enterprise_search_history');
        return history ? JSON.parse(history) : [];
    } catch (error) {
        console.warn('加载搜索历史失败:', error);
        return [];
    }
}

/**
 * 保存搜索历史到本地存储
 */
saveSearchHistory() {
    try {
        localStorage.setItem('enterprise_search_history', 
            JSON.stringify(this.searchState.searchHistory));
    } catch (error) {
        console.warn('保存搜索历史失败:', error);
    }
}

// 4. 修改 onTabSwitch 方法，添加搜索统计更新
// 在现有的 onTabSwitch 方法中，添加以下代码：
// this.updateSearchStats(tabId);

/*
使用说明：
1. 将上述代码添加到 DesktopDataGridManager 类中
2. 确保构造函数中已添加搜索状态管理（已完成）
3. 确保 init() 方法中调用了 this.initSearchSystem()（已完成）
4. 确保 HTML 和 CSS 已正确更新
5. 测试搜索功能是否正常工作
*/ 